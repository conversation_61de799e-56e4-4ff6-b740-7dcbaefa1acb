import React, { useState } from "react";
import {
  Box,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Link,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Formik, Form } from "formik";
import * as yup from "yup";

// Define the form values type
interface ParkingFormValues {
  freeMultiStoreyCarPark: string;
  freeOfChargeStreetParking: string;
  freeParkingLot: string;
  onSiteParking: string;
  paidMultiStoreyCarPark: string;
  paidParkingLot: string;
}

interface ParkingProps {
  onSave?: (values: ParkingFormValues) => void;
  initialValues?: ParkingFormValues;
}

const Parking: React.FC<ParkingProps> = ({ onSave, initialValues }) => {
  // State for modal
  const [isOpen, setIsOpen] = useState(false);

  // Default initial values
  const defaultValues: ParkingFormValues = {
    freeMultiStoreyCarPark: "",
    freeOfChargeStreetParking: "",
    freeParkingLot: "",
    onSiteParking: "",
    paidMultiStoreyCarPark: "",
    paidParkingLot: "",
  };

  // Validation schema
  const parkingSchema = yup.object({
    freeMultiStoreyCarPark: yup.string().required("Please select an option"),
    freeOfChargeStreetParking: yup.string().required("Please select an option"),
    freeParkingLot: yup.string().required("Please select an option"),
    onSiteParking: yup.string().required("Please select an option"),
    paidMultiStoreyCarPark: yup.string().required("Please select an option"),
    paidParkingLot: yup.string().required("Please select an option"),
  });

  // Handle form submission
  const handleSubmit = (values: ParkingFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Parking options saved:", values);
    setIsOpen(false);
  };

  // Parking options
  const parkingOptions = [
    {
      id: "freeMultiStoreyCarPark",
      label: "Free multi-storey car park",
    },
    {
      id: "freeOfChargeStreetParking",
      label: "Free of charge street parking",
    },
    {
      id: "freeParkingLot",
      label: "Free parking lot",
    },
    {
      id: "onSiteParking",
      label: "On-site parking",
    },
    {
      id: "paidMultiStoreyCarPark",
      label: "Paid multi-storey car park",
    },
    {
      id: "paidParkingLot",
      label: "Paid parking lot",
    },
  ];

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        sx={{ textTransform: "none" }}
      >
        Parking
      </Button>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
            }}
          >
            Parking
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={() => setIsOpen(false)}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 3, bgcolor: "white" }}>
          <Formik
            initialValues={initialValues || defaultValues}
            validationSchema={parkingSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleSubmit, errors, touched }) => (
              <Form onSubmit={handleSubmit}>
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline",
                    }}
                  >
                    Let customers know more about your business by showing
                    attributes on your Business Profile. These may appear
                    publicly on Search, Maps and other Google services.{" "}
                  </Typography>
                  <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link>
                </Box>

                <Box sx={{ mb: 4 }}>
                  {parkingOptions.map((option) => (
                    <Box key={option.id} sx={{ mb: 3 }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          color: "black",
                          mb: 1,
                          fontWeight: 500,
                        }}
                      >
                        {option.label}
                      </Typography>

                      <RadioGroup
                        name={option.id}
                        value={values[option.id as keyof ParkingFormValues]}
                        onChange={handleChange}
                        row
                      >
                        <FormControlLabel
                          value="Yes"
                          control={
                            <Radio
                              sx={{
                                color: "#1976d2",
                                "&.Mui-checked": {
                                  color: "#1976d2",
                                },
                              }}
                            />
                          }
                          label={
                            <Typography sx={{ color: "black" }}>Yes</Typography>
                          }
                          sx={{ mr: 4 }}
                        />
                        <FormControlLabel
                          value="No"
                          control={
                            <Radio
                              sx={{
                                color: "#1976d2",
                                "&.Mui-checked": {
                                  color: "#1976d2",
                                },
                              }}
                            />
                          }
                          label={
                            <Typography sx={{ color: "black" }}>No</Typography>
                          }
                        />
                      </RadioGroup>

                      {errors[option.id as keyof ParkingFormValues] &&
                        touched[option.id as keyof ParkingFormValues] && (
                          <Typography color="error" variant="caption">
                            {errors[option.id as keyof ParkingFormValues]}
                          </Typography>
                        )}
                    </Box>
                  ))}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    mt: 3,
                    gap: 2,
                  }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: "none",
                      bgcolor: "#1976d2",
                      color: "white",
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setIsOpen(false)}
                    sx={{
                      textTransform: "none",
                      color: "#1976d2",
                      borderColor: "#e0e0e0",
                      bgcolor: "white",
                    }}
                    startIcon={<CancelOutlinedIcon />}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Parking;
