import React, { useContext, useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import { IUserRequestModel } from "../../interfaces/request/IUserRequestModel";
import { IUserResponseModel } from "../../interfaces/response/IUserResponseModel";
import UserService from "../../services/user/user.service";
import * as yup from "yup";
import { Formik } from "formik";
import { ToastContext } from "../../context/toast.context";
import {
  FormHelperText,
  IconButton,
  InputAdornment,
  Checkbox,
} from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import {
  IRole,
  IRolesResponseModel,
} from "../../interfaces/response/IRolesResponseModel";
import RolesService from "../../services/roles/roles.service";
import { useDispatch, useSelector } from "react-redux";
import InputLabel from "@mui/material/InputLabel";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import BusinessService from "../../services/business/business.service";
import OutlinedInput from "@mui/material/OutlinedInput";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import { RoleType } from "../../constants/dbConstant.constant";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import Chip from "@mui/material/Chip";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import { LoadingContext } from "../../context/loading.context";
import RemoveRedEyeOutlinedIcon from "@mui/icons-material/RemoveRedEyeOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import { IUserCreationResponseModel } from "../../interfaces/response/IUserCreationResponseModel";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";
import { IUser } from "../../interfaces/response/IUsersListResponse";
import {
  IUserLocation,
  IUserLocationsResponseModel,
} from "../../interfaces/response/IUserLocationsResponseModel";
import { IAlertDialogConfig } from "../../interfaces/IAlertDialogConfig";
import { logOut } from "../../actions/auth.actions";
import AlertDialog from "../alertDialog/alertDialog.component";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

const AddEditUserComponent = (props: {
  userId: number;
  editData: IUserRequestModel | null | undefined;
  callBack: (
    editData: IUserRequestModel | undefined,
    response: IUserCreationResponseModel | undefined
  ) => null | void | undefined;
  showResetPasswordDialog: () => null | void | undefined;
}) => {
  const dispatch = useDispatch();

  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const { setLoading } = useContext(LoadingContext);
  const _userService = new UserService(dispatch);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const [rolesList, setRolesList] = useState<IRole[]>([]);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [locations, setLocations] = useState<ILocation[]>([]);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);
  const INITIAL_VALUES = {
    roleId: 0,
    name: "",
    mobile: "",
    email: "",
    password: "",
    confirmPassword: "",
    statusId: 1,
    businessId: 0,
    accountId: [],
    locationId: [],
  };
  const [initialValues, setInitialValues] =
    useState<IUserRequestModel>(INITIAL_VALUES);
  useEffect(() => {}, []);
  const _rolesService = new RolesService(dispatch);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    if (props.editData && (props.editData as any).id > 0) {
      getBusinessIds((props.editData as any).id, props.editData.roleId);
    } else {
      setInitialValues({
        roleId: 0,
        name: "",
        mobile: "",
        email: "",
        password: "",
        confirmPassword: "",
        statusId: 1,
        businessId: null,
        accountId: [],
        locationId: [],
      });
    }

    async function getBusinessIds(userId: number, roleId: number) {
      const locationsList: IUserLocationsResponseModel =
        await _userService.getUserLocationList(userId);
      if (props.editData) {
        await getBusiness();
        await getBusinessGroups();
        await getLocationsList();
        setInitialValues({
          roleId: props.editData.roleId,
          name: props.editData.name,
          mobile: props.editData.mobile,
          email: props.editData.email,
          password: "",
          confirmPassword: "",
          statusId: 1,
          businessId:
            locationsList.list.length > 0
              ? locationsList.list[0].businessId
              : 0,
          accountId:
            locationsList.list.length > 0
              ? locationsList.list
                  .map((x: IUserLocation) => x.gmbAccountId)
                  .filter((x, i, a) => a.indexOf(x) == i)
              : [],
          locationId:
            locationsList.list.length > 0
              ? locationsList.list.map((x: IUserLocation) => x.gmbLocationId)
              : [],
        });
      }
    }
  }, []);

  const UserSchema = yup.object().shape({
    roleId: yup
      .number()
      .required("This field is required")
      .moreThan(0, "Select valid Role"),
    name: yup.string().required("This field is required"),
    mobile: yup.number().required("This field is required"),
    email: yup
      .string()
      .email("Please enter a valid email")
      .required("This field is required"),
    password:
      props.userId && props.userId > 0
        ? yup.string()
        : yup.string().required("This field is required"),
    statusId: yup.number().required("This field is required"),
    businessId: yup
      .number()
      .nullable()
      .when("$roleId", (roleId, schema) => {
        if (
          (roleId && roleId[0] === RoleType.Manager) ||
          roleId[0] === RoleType.User
        ) {
          return schema.nonNullable().required("This field is required");
        }
        return schema; // Keep it nullable for other types
      })
      .test({
        name: "mandatory-check-business-id",
        message: "This field is required",
        test: function (value) {
          const { from } = this;
          const objectValues = from as any;
          const values = objectValues[objectValues.length - 1]
            .value as IUserRequestModel;

          // Validate only if topicType is "Event or Offer"
          if (
            values.roleId === RoleType.Manager ||
            values.roleId === RoleType.User
          ) {
            return Boolean(value && value > 0);
          }
          return true; // Skip validation for other types
        },
      }),
    accountId: yup
      .array()
      .of(yup.string())
      .nullable()
      .when("$roleId", (roleId, schema) => {
        if (roleId && roleId[0] === RoleType.User) {
          return schema.nonNullable().required("This field is required");
        }
        return schema; // Keep it nullable for other types
      })
      .test({
        name: "mandatory-check-account-id",
        message: "This field is required",
        test: function (value) {
          const { from } = this;
          const objectValues = from as any;
          const values = objectValues[objectValues.length - 1]
            .value as IUserRequestModel;

          // Validate only if topicType is "Event or Offer"
          if (values.roleId === RoleType.User) {
            return Boolean(value && value.length > 0);
          }
          return true; // Skip validation for other types
        },
      }),
    locationId: yup
      .array()
      .of(yup.string())
      .nullable()
      .when("$roleId", (roleId, schema) => {
        if (roleId && roleId[0] === RoleType.User) {
          return schema.nonNullable().required("This field is required");
        }
        return schema; // Keep it nullable for other types
      })
      .test({
        name: "mandatory-check-location-id",
        message: "This field is required",
        test: function (value) {
          const { from } = this;
          const objectValues = from as any;
          const values = objectValues[objectValues.length - 1]
            .value as IUserRequestModel;

          // Validate only if topicType is "Event or Offer"
          if (values.roleId === RoleType.User) {
            return Boolean(value && value.length > 0);
          }
          return true; // Skip validation for other types
        },
      }),
  });

  const getRolesList = async () => {
    try {
      var roles: IRolesResponseModel = await _rolesService.roleList(
        userInfo.id
      );
      if (roles.list.length > 0) {
        setRolesList(roles.list);
      }
    } catch (error) {}
  };

  const getBusiness = async () => {
    try {
      setLoading(true);
      let businesses: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (businesses.list.length > 0) {
        const uniqueBusinesses = [];
        const seenIds = new Set();

        for (const item of businesses.list) {
          if (!seenIds.has(item.id)) {
            seenIds.add(item.id);
            uniqueBusinesses.push({
              id: item.id,
              businessName: item.businessName,
            });
          }
        }

        console.log(uniqueBusinesses);

        setBusinessList(uniqueBusinesses as IBusiness[]);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getLocationsList = async () => {
    try {
      setLoading(true);
      let locationsList: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (locationsList.list.length > 0) {
        setLocations(locationsList.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  const _handleSaveAccount = async (values: IUserRequestModel) => {
    try {
      const isValid = await UserSchema.isValid(values);
      if (isValid) {
        if (props.userId && props.userId > 0) {
          await _userService.updateUserCombined({
            ...values,
            userId: props.userId,
          });
          props.callBack(undefined, undefined);
          setToastConfig(
            ToastSeverity.Success,
            MessageConstants.UpdatedSuccessfully,
            true
          );
        } else {
          var response: IUserCreationResponseModel =
            await _userService.createUser(values);
          if (response && response.userId > 0) {
            props.callBack(values, response);
          }
        }
      }
    } catch (error: any) {
      if (error.response.data) {
        switch (error.response.data.error.code) {
          case "ER_DUP_ENTRY":
            setToastConfig(
              ToastSeverity.Error,
              error.response.data.error.sqlMessage.split("for")[0],
              true
            );
            break;
          default:
            setToastConfig(
              ToastSeverity.Error,
              error.response.data.error,
              true
            );
            break;
        }
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.ApiErrorStandardMessage,
          true
        );
      }
    }
  };

  useEffect(() => {
    getRolesList();
  }, []);

  const handleValidation = (values: IUserRequestModel) => {
    if (
      values.roleId &&
      (values.roleId === RoleType.User || values.roleId === RoleType.Manager) &&
      businessList.length === 0
    ) {
      getBusiness();
      getLocationsList();
      getBusinessGroups();
    }
  };

  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  return (
    <>
      <Formik
        enableReinitialize
        initialValues={{ ...initialValues }}
        validationSchema={UserSchema}
        onSubmit={(values) => {
          _handleSaveAccount(values);
        }}
        validate={handleValidation}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleSubmit,
          setFieldValue,
          handleReset,
          /* and other goodies */
        }) => (
          <form
            onSubmit={handleSubmit}
            onReset={handleReset}
            className="commonModal"
          >
            <Box className="height100">
              <Typography
                id="modal-modal-title"
                variant="h6"
                component="h2"
                className="modal-modal-title"
              >
                {props.userId > 0 ? "Edit" : "Add"} User
              </Typography>

              <Box
                id="modal-modal-description"
                className="modal-modal-description"
              >
                <Box>
                  <Box className="commonInput">
                    <TextField
                      id="name"
                      label="Name"
                      variant="outlined"
                      fullWidth
                      onChange={handleChange}
                      value={values.name}
                      disabled={props.userId > 0}
                    />
                    {errors.name && touched.name && (
                      <FormHelperText className="errorMessage">
                        {errors.name}
                      </FormHelperText>
                    )}
                  </Box>
                  <Box className="commonInput">
                    <TextField
                      id="email"
                      label="Email ID"
                      variant="outlined"
                      fullWidth
                      onChange={handleChange}
                      value={values.email}
                      disabled={props.userId > 0}
                    />
                    {errors.email && touched.email && (
                      <FormHelperText className="errorMessage">
                        {errors.email}
                      </FormHelperText>
                    )}
                  </Box>
                  <Box className="commonInput">
                    <FormControl variant="outlined" fullWidth>
                      <TextField
                        id="mobile"
                        label="Phone"
                        variant="outlined"
                        fullWidth
                        onChange={handleChange}
                        value={values.mobile}
                      />
                      {errors.mobile && touched.mobile && (
                        <FormHelperText className="errorMessage">
                          {errors.mobile}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>
                  <Box className="commonInput">
                    <FormControl variant="outlined" fullWidth>
                      <InputLabel id="outlined-country-dropdown-label">
                        Role
                      </InputLabel>

                      <Select
                        fullWidth
                        id="roleId"
                        label="Role"
                        value={values.roleId.toString()}
                        onChange={(evt: SelectChangeEvent) => {
                          setFieldValue("accountId", []);
                          setFieldValue("locationId", []);
                          setFieldValue("businessId", 0);
                          setFieldValue("roleId", +evt.target.value);
                        }}
                        sx={{
                          backgroundColor: "var(--whiteColor)",
                          borderRadius: "5px",
                        }}
                        disabled={
                          props.editData
                            ? userInfo.id === (props.editData as any).id
                            : false
                        }
                      >
                        <MenuItem value={0}>Select</MenuItem>
                        {rolesList &&
                          rolesList.map((role: IRole) => (
                            <MenuItem key={role.id} value={role.id.toString()}>
                              {role.role}
                            </MenuItem>
                          ))}
                      </Select>
                      {errors.roleId && touched.roleId && (
                        <FormHelperText className="errorMessage">
                          {errors.roleId}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>

                  <Box className="commonInput">
                    <FormControl variant="outlined" fullWidth>
                      <TextField
                        id="password"
                        label="Password"
                        variant="outlined"
                        fullWidth
                        onChange={handleChange}
                        value={values.password}
                        type={showPassword ? "text" : "password"}
                        slotProps={{
                          input: {
                            startAdornment: (
                              <InputAdornment position="start">
                                <IconButton
                                  aria-label="settings"
                                  className="iconBtn"
                                  onClick={() => setShowPassword(!showPassword)}
                                >
                                  {showPassword ? (
                                    <VisibilityOffOutlinedIcon />
                                  ) : (
                                    <RemoveRedEyeOutlinedIcon />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                      {errors.password && touched.password && (
                        <FormHelperText className="errorMessage">
                          {errors.password}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>
                  <Box className="commonInput">
                    <FormControl variant="outlined" fullWidth>
                      <TextField
                        id="confirmPassword"
                        label="Confirm Password"
                        variant="outlined"
                        fullWidth
                        onChange={handleChange}
                        value={values.confirmPassword}
                        type={showConfirmPassword ? "text" : "password"}
                        slotProps={{
                          input: {
                            startAdornment: (
                              <InputAdornment position="start">
                                <IconButton
                                  aria-label="settings"
                                  className="iconBtn"
                                  onClick={() =>
                                    setShowConfirmPassword(!showConfirmPassword)
                                  }
                                >
                                  {showConfirmPassword ? (
                                    <VisibilityOffOutlinedIcon />
                                  ) : (
                                    <RemoveRedEyeOutlinedIcon />
                                  )}
                                </IconButton>
                              </InputAdornment>
                            ),
                          },
                        }}
                      />
                      {errors.confirmPassword && touched.confirmPassword && (
                        <FormHelperText className="errorMessage">
                          {errors.confirmPassword}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Box>
                  {values &&
                    values.roleId > 0 &&
                    (values.roleId === RoleType.Manager ||
                      values.roleId === RoleType.User) &&
                    businessList && (
                      <Box>
                        <Box className="commonInput">
                          <FormControl variant="outlined" fullWidth>
                            <InputLabel id="outlined-country-dropdown-label">
                              Business
                            </InputLabel>

                            <Select
                              fullWidth
                              id="businessId"
                              label="Business"
                              value={values.businessId?.toString()}
                              onChange={(evt: SelectChangeEvent) => {
                                setFieldValue("accountId", []);
                                setFieldValue("locationId", []);
                                setTimeout(() => {
                                  setFieldValue(
                                    "businessId",
                                    +evt.target.value
                                  );
                                }, 500);
                              }}
                              sx={{
                                backgroundColor: "var(--whiteColor)",
                                borderRadius: "5px",
                              }}
                            >
                              <MenuItem value={0}>Select</MenuItem>
                              {businessList &&
                                businessList.map((business: IBusiness) => (
                                  <MenuItem
                                    key={business.id}
                                    value={business.id.toString()}
                                  >
                                    {business.businessName}
                                  </MenuItem>
                                ))}
                            </Select>
                            {errors.businessId && touched.businessId && (
                              <FormHelperText className="errorMessage">
                                {errors.businessId}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Box>
                      </Box>
                    )}
                  {values &&
                    values.roleId > 0 &&
                    values.roleId === RoleType.User &&
                    businessList && (
                      <Box>
                        <Box className="commonInput">
                          <FormControl variant="outlined" fullWidth>
                            <InputLabel id="demo-multiple-name-label">
                              Groups
                            </InputLabel>
                            <Select
                              id="accountId"
                              multiple={true}
                              value={values.accountId}
                              onChange={(
                                event: SelectChangeEvent<string[]>
                              ) => {
                                setFieldValue("locationId", []);
                                const {
                                  target: { value },
                                } = event;

                                // Get all available group IDs for the current business
                                const availableGroups = businessGroups
                                  .filter(
                                    (x) => x.businessId === values.businessId
                                  )
                                  .map((group) => group.accountId);

                                // Special handling for "Select All" option
                                if (value.includes("select-all")) {
                                  // If the current selection already includes "select-all"
                                  if (values.accountId.includes("select-all")) {
                                    // User clicked "Select All" again to deselect all
                                    setFieldValue("accountId", []);
                                    // Clear locations when deselecting all groups
                                    setFieldValue("locationId", []);
                                  } else {
                                    // User clicked "Select All" to select all
                                    setFieldValue("accountId", [
                                      ...availableGroups,
                                      "select-all",
                                    ]);
                                  }
                                } else {
                                  // Regular selection (not involving direct "Select All" click)

                                  // Check if all available items are selected
                                  const allSelected =
                                    availableGroups.length > 0 &&
                                    availableGroups.every((group) =>
                                      value.includes(group)
                                    );

                                  // Check if we're deselecting the last item
                                  const isDeselectingLastItem =
                                    values.accountId.includes("select-all") &&
                                    value.length < availableGroups.length;

                                  if (allSelected) {
                                    // All items are manually selected, add "select-all"
                                    setFieldValue("accountId", [
                                      ...value,
                                      "select-all",
                                    ]);
                                  } else if (isDeselectingLastItem) {
                                    // We're deselecting an item after "Select All" was checked
                                    // Just update with the current selection, removing "select-all"
                                    setFieldValue("accountId", value);

                                    // Clear locations when changing groups
                                    setFieldValue("locationId", []);
                                  } else {
                                    // Normal selection update
                                    setFieldValue("accountId", value);

                                    // Clear locations when changing groups
                                    setFieldValue("locationId", []);
                                  }
                                }
                              }}
                              input={<OutlinedInput label="Groups" />}
                              renderValue={(selected) => {
                                // Don't show "Select All" in the chips
                                const displayValues = selected.filter(
                                  (value) => value !== "select-all"
                                );
                                return (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      flexWrap: "wrap",
                                      gap: 0.5,
                                    }}
                                  >
                                    {displayValues.length === 0 ? (
                                      <em>Select Groups</em>
                                    ) : (
                                      displayValues.map((value) => {
                                        const group = businessGroups.find(
                                          (x) => x.accountId === value
                                        );
                                        return group ? (
                                          <Chip
                                            key={value}
                                            label={group.accountName}
                                            size="small"
                                          />
                                        ) : null;
                                      })
                                    )}
                                  </Box>
                                );
                              }}
                              MenuProps={{
                                ...MenuProps,
                                PaperProps: {
                                  ...MenuProps.PaperProps,
                                  style: {
                                    ...MenuProps.PaperProps.style,
                                    maxHeight:
                                      ITEM_HEIGHT * 6.5 + ITEM_PADDING_TOP,
                                    maxWidth: "100%",
                                    width: "auto",
                                  },
                                },
                                // Ensure the dropdown matches the select width
                                MenuListProps: {
                                  style: { paddingTop: 0, paddingBottom: 0 },
                                },
                              }}
                              sx={{
                                backgroundColor: "var(--whiteColor)",
                                borderRadius: "5px",
                              }}
                            >
                              {businessGroups.filter(
                                (x) => x.businessId === values.businessId
                              ).length > 0 ? (
                                <>
                                  <MenuItem
                                    value="select-all"
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(0, 0, 0, 0.12)",
                                      fontWeight: "bold",
                                      paddingY: 1,
                                    }}
                                    onClick={() => {
                                      // Handle direct click on the "Select All" MenuItem
                                      if (
                                        values.accountId.includes("select-all")
                                      ) {
                                        setFieldValue("accountId", []);
                                        setFieldValue("locationId", []);
                                      } else {
                                        const availableGroupIds = businessGroups
                                          .filter(
                                            (x) =>
                                              x.businessId === values.businessId
                                          )
                                          .map((group) => group.accountId);
                                        setFieldValue("accountId", [
                                          ...availableGroupIds,
                                          "select-all",
                                        ]);
                                      }
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        width: "100%",
                                        flexDirection: "row",
                                      }}
                                    >
                                      <Checkbox
                                        checked={values.accountId.includes(
                                          "select-all"
                                        )}
                                        indeterminate={
                                          !values.accountId.includes(
                                            "select-all"
                                          ) &&
                                          values.accountId.length > 0 &&
                                          values.accountId.length <
                                            businessGroups.filter(
                                              (x) =>
                                                x.businessId ===
                                                values.businessId
                                            ).length
                                        }
                                        sx={{
                                          padding: "0 9px 0 0",
                                          marginRight: "4px",
                                        }}
                                      />
                                      <Typography
                                        sx={{
                                          fontWeight: "bold",
                                          flex: 1,
                                          display: "inline-block",
                                          verticalAlign: "middle",
                                        }}
                                      >
                                        Select All
                                      </Typography>
                                    </Box>
                                  </MenuItem>
                                  {businessGroups
                                    .filter(
                                      (x) => x.businessId === values.businessId
                                    )
                                    .map((businessGroup: IBusinessGroup) => (
                                      <MenuItem
                                        key={businessGroup.accountId}
                                        value={businessGroup.accountId}
                                        sx={{
                                          whiteSpace: "normal",
                                          wordWrap: "break-word",
                                          paddingY: 1,
                                          width: "100%",
                                          overflowX: "hidden",
                                          textOverflow: "ellipsis",
                                        }}
                                        onClick={() => {
                                          // Handle direct click on individual group
                                          const newValue = [
                                            ...values.accountId,
                                          ];
                                          const index = newValue.indexOf(
                                            businessGroup.accountId
                                          );

                                          if (index === -1) {
                                            // Add the group if not already selected
                                            newValue.push(
                                              businessGroup.accountId
                                            );
                                          } else {
                                            // Remove the group if already selected
                                            newValue.splice(index, 1);
                                          }

                                          // Remove "select-all" if any group is deselected
                                          const selectAllIndex =
                                            newValue.indexOf("select-all");
                                          if (
                                            index !== -1 &&
                                            selectAllIndex !== -1
                                          ) {
                                            newValue.splice(selectAllIndex, 1);
                                          }

                                          // Check if all groups are selected to add "select-all"
                                          const availableGroupIds =
                                            businessGroups
                                              .filter(
                                                (x) =>
                                                  x.businessId ===
                                                  values.businessId
                                              )
                                              .map((group) => group.accountId);

                                          const allSelected =
                                            availableGroupIds.length > 0 &&
                                            availableGroupIds.every((group) =>
                                              newValue.includes(group)
                                            );

                                          if (
                                            allSelected &&
                                            !newValue.includes("select-all")
                                          ) {
                                            newValue.push("select-all");
                                          }

                                          setFieldValue("accountId", newValue);

                                          // Clear locations when changing groups
                                          setFieldValue("locationId", []);
                                        }}
                                      >
                                        <Box
                                          sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            width: "100%",
                                            flexDirection: "row",
                                          }}
                                        >
                                          <Checkbox
                                            checked={values.accountId.includes(
                                              businessGroup.accountId
                                            )}
                                            sx={{
                                              padding: "0 9px 0 0",
                                              marginRight: "4px",
                                            }}
                                          />
                                          <Typography
                                            sx={{
                                              fontSize: "0.875rem",
                                              lineHeight: 1.4,
                                              flex: 1,
                                              display: "inline-block",
                                              verticalAlign: "middle",
                                            }}
                                          >
                                            {businessGroup.accountName}
                                          </Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                </>
                              ) : (
                                <MenuItem disabled>
                                  <em>No Groups Found</em>
                                </MenuItem>
                              )}
                            </Select>
                            {errors.accountId && touched.accountId && (
                              <FormHelperText className="errorMessage">
                                {errors.accountId}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Box>

                        <Box className="commonInput">
                          <FormControl variant="outlined" fullWidth>
                            <InputLabel id="demo-multiple-name-label">
                              Locations
                            </InputLabel>
                            <Select
                              id="locationId"
                              multiple={true}
                              value={values.locationId}
                              onChange={(
                                event: SelectChangeEvent<string[]>
                              ) => {
                                const {
                                  target: { value },
                                } = event;

                                // Get all available location IDs for the selected groups
                                const availableLocations = locations
                                  .filter(
                                    (x) =>
                                      values.accountId.includes(
                                        x.gmbAccountId
                                      ) &&
                                      // Filter out "select-all" from accountId
                                      x.gmbAccountId !== "select-all"
                                  )
                                  .map((location) => location.gmbLocationId);

                                // Special handling for "Select All" option
                                if (value.includes("select-all")) {
                                  // If the current selection already includes "select-all"
                                  if (
                                    values.locationId.includes("select-all")
                                  ) {
                                    // User clicked "Select All" again to deselect all
                                    setFieldValue("locationId", []);
                                  } else {
                                    // User clicked "Select All" to select all
                                    setFieldValue("locationId", [
                                      ...availableLocations,
                                      "select-all",
                                    ]);
                                  }
                                } else {
                                  // Regular selection (not involving direct "Select All" click)

                                  // Check if all available items are selected
                                  const allSelected =
                                    availableLocations.length > 0 &&
                                    availableLocations.every((loc) =>
                                      value.includes(loc)
                                    );

                                  // Check if we're deselecting the last item
                                  const isDeselectingLastItem =
                                    values.locationId.includes("select-all") &&
                                    value.length < availableLocations.length;

                                  if (allSelected) {
                                    // All items are manually selected, add "select-all"
                                    setFieldValue("locationId", [
                                      ...value,
                                      "select-all",
                                    ]);
                                  } else if (isDeselectingLastItem) {
                                    // We're deselecting an item after "Select All" was checked
                                    // Just update with the current selection, removing "select-all"
                                    setFieldValue("locationId", value);
                                  } else {
                                    // Normal selection update
                                    setFieldValue("locationId", value);
                                  }
                                }
                              }}
                              input={<OutlinedInput label="Locations" />}
                              renderValue={(selected) => {
                                // Don't show "Select All" in the chips
                                const displayValues = selected.filter(
                                  (value) => value !== "select-all"
                                );
                                return (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      flexWrap: "wrap",
                                      gap: 0.5,
                                    }}
                                  >
                                    {displayValues.length === 0 ? (
                                      <em>Select Locations</em>
                                    ) : (
                                      displayValues.map((value) => {
                                        const location = locations.find(
                                          (x) => x.gmbLocationId === value
                                        );
                                        return location ? (
                                          <Chip
                                            key={value}
                                            label={location.gmbLocationName}
                                            size="small"
                                          />
                                        ) : null;
                                      })
                                    )}
                                  </Box>
                                );
                              }}
                              MenuProps={{
                                ...MenuProps,
                                PaperProps: {
                                  ...MenuProps.PaperProps,
                                  style: {
                                    ...MenuProps.PaperProps.style,
                                    maxHeight:
                                      ITEM_HEIGHT * 6.5 + ITEM_PADDING_TOP,
                                    maxWidth: "100%",
                                    width: "auto",
                                  },
                                },
                                // Ensure the dropdown matches the select width
                                MenuListProps: {
                                  style: { paddingTop: 0, paddingBottom: 0 },
                                },
                              }}
                              sx={{
                                backgroundColor: "var(--whiteColor)",
                                borderRadius: "5px",
                              }}
                            >
                              {/* Only show options if groups are selected */}
                              {values.accountId.filter(
                                (id) => id !== "select-all"
                              ).length > 0 ? (
                                <>
                                  {/* Select All option */}
                                  <MenuItem
                                    value="select-all"
                                    sx={{
                                      borderBottom:
                                        "1px solid rgba(0, 0, 0, 0.12)",
                                      fontWeight: "bold",
                                      paddingY: 1,
                                    }}
                                    onClick={() => {
                                      // Handle direct click on the "Select All" MenuItem
                                      if (
                                        values.locationId.includes("select-all")
                                      ) {
                                        setFieldValue("locationId", []);
                                      } else {
                                        const availableLocationIds = locations
                                          .filter(
                                            (x) =>
                                              values.accountId.includes(
                                                x.gmbAccountId
                                              ) &&
                                              x.gmbAccountId !== "select-all"
                                          )
                                          .map(
                                            (location) => location.gmbLocationId
                                          );
                                        setFieldValue("locationId", [
                                          ...availableLocationIds,
                                          "select-all",
                                        ]);
                                      }
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        width: "100%",
                                        flexDirection: "row",
                                      }}
                                    >
                                      <Checkbox
                                        checked={values.locationId.includes(
                                          "select-all"
                                        )}
                                        indeterminate={
                                          !values.locationId.includes(
                                            "select-all"
                                          ) &&
                                          values.locationId.length > 0 &&
                                          values.locationId.length <
                                            locations.filter(
                                              (x) =>
                                                values.accountId.includes(
                                                  x.gmbAccountId
                                                ) &&
                                                x.gmbAccountId !== "select-all"
                                            ).length
                                        }
                                        sx={{
                                          padding: "0 9px 0 0",
                                          marginRight: "4px",
                                        }}
                                      />
                                      <Typography
                                        sx={{
                                          fontWeight: "bold",
                                          flex: 1,
                                          display: "inline-block",
                                          verticalAlign: "middle",
                                        }}
                                      >
                                        Select All
                                      </Typography>
                                    </Box>
                                  </MenuItem>

                                  {/* Individual location options */}
                                  {locations
                                    .filter(
                                      (x) =>
                                        values.accountId.includes(
                                          x.gmbAccountId
                                        ) && x.gmbAccountId !== "select-all"
                                    )
                                    .map((location: ILocation) => (
                                      <MenuItem
                                        key={location.gmbLocationId}
                                        value={location.gmbLocationId}
                                        sx={{
                                          whiteSpace: "normal",
                                          wordWrap: "break-word",
                                          paddingY: 1,
                                          width: "100%",
                                          overflowX: "hidden",
                                          textOverflow: "ellipsis",
                                        }}
                                        onClick={() => {
                                          // Handle direct click on individual location
                                          const newValue = [
                                            ...values.locationId,
                                          ];
                                          const index = newValue.indexOf(
                                            location.gmbLocationId
                                          );

                                          if (index === -1) {
                                            // Add the location if not already selected
                                            newValue.push(
                                              location.gmbLocationId
                                            );
                                          } else {
                                            // Remove the location if already selected
                                            newValue.splice(index, 1);
                                          }

                                          // Remove "select-all" if any location is deselected
                                          const selectAllIndex =
                                            newValue.indexOf("select-all");
                                          if (
                                            index !== -1 &&
                                            selectAllIndex !== -1
                                          ) {
                                            newValue.splice(selectAllIndex, 1);
                                          }

                                          // Check if all locations are selected to add "select-all"
                                          const availableLocationIds = locations
                                            .filter(
                                              (x) =>
                                                values.accountId.includes(
                                                  x.gmbAccountId
                                                ) &&
                                                x.gmbAccountId !== "select-all"
                                            )
                                            .map((loc) => loc.gmbLocationId);

                                          const allSelected =
                                            availableLocationIds.length > 0 &&
                                            availableLocationIds.every((loc) =>
                                              newValue.includes(loc)
                                            );

                                          if (
                                            allSelected &&
                                            !newValue.includes("select-all")
                                          ) {
                                            newValue.push("select-all");
                                          }

                                          setFieldValue("locationId", newValue);
                                        }}
                                      >
                                        <Box
                                          sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            width: "100%",
                                            flexDirection: "row",
                                          }}
                                        >
                                          <Checkbox
                                            checked={values.locationId.includes(
                                              location.gmbLocationId
                                            )}
                                            sx={{
                                              padding: "0 9px 0 0",
                                              marginRight: "4px",
                                            }}
                                          />
                                          <Typography
                                            sx={{
                                              fontSize: "0.875rem",
                                              lineHeight: 1.4,
                                              flex: 1,
                                              display: "inline-block",
                                              verticalAlign: "middle",
                                            }}
                                          >
                                            {location.gmbLocationName}
                                          </Typography>
                                        </Box>
                                      </MenuItem>
                                    ))}
                                </>
                              ) : (
                                <MenuItem disabled>
                                  <em>Please select Groups first</em>
                                </MenuItem>
                              )}
                            </Select>
                            {errors.locationId && touched.locationId && (
                              <FormHelperText className="errorMessage">
                                {errors.locationId}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Box>
                      </Box>
                    )}
                </Box>
              </Box>

              <Box className="">
                <Stack direction="row" className="commonFooter">
                  <Button
                    variant="contained"
                    className="closeFillBtn"
                    onClick={() => props.callBack(undefined, undefined)}
                    startIcon={<CancelOutlinedIcon />}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    className="primaryFillBtn"
                    type="submit"
                  >
                    Save User
                  </Button>
                </Stack>
              </Box>
            </Box>
          </form>
        )}
      </Formik>
    </>
  );
};

export default AddEditUserComponent;
