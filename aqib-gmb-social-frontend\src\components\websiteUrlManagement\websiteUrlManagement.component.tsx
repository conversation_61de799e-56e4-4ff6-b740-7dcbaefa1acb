import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  useMediaQuery,
  useTheme,
  Link,
  Alert,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import LanguageIcon from "@mui/icons-material/Language";
import { Formik, Form, FormikHelpers } from "formik";
import * as yup from "yup";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

interface WebsiteUrlFormValues {
  websiteUrl: string;
}

interface WebsiteUrlManagementProps {
  open: boolean;
  onClose: () => void;
  currentWebsiteUrl: string;
  onUpdateWebsiteUrl: (websiteUrl: string) => void;
}

const WebsiteUrlManagement: React.FC<WebsiteUrlManagementProps> = ({
  open,
  onClose,
  currentWebsiteUrl,
  onUpdateWebsiteUrl,
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validationSchema = yup.object({
    websiteUrl: yup
      .string()
      .url("Please enter a valid URL (e.g., https://example.com)")
      .required("Website URL is required")
      .matches(/^https?:\/\/.+/, "URL must start with http:// or https://"),
  });

  const initialValues: WebsiteUrlFormValues = {
    websiteUrl: currentWebsiteUrl || "",
  };

  const handleSubmit = async (
    values: WebsiteUrlFormValues,
    { setSubmitting }: FormikHelpers<WebsiteUrlFormValues>
  ) => {
    try {
      setIsSubmitting(true);

      // Ensure URL has protocol
      let formattedUrl = values.websiteUrl.trim();
      if (
        !formattedUrl.startsWith("http://") &&
        !formattedUrl.startsWith("https://")
      ) {
        formattedUrl = "https://" + formattedUrl;
      }

      onUpdateWebsiteUrl(formattedUrl);
      onClose();
    } catch (error) {
      console.error("Error updating website URL:", error);
    } finally {
      setSubmitting(false);
      setIsSubmitting(false);
    }
  };

  const handleRemoveWebsite = () => {
    onUpdateWebsiteUrl("");
    onClose();
  };

  const formatUrlForDisplay = (url: string) => {
    if (!url) return "";
    return url.replace(/^https?:\/\//, "").replace(/\/$/, "");
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={fullScreen}
      className="commonDialog"
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxWidth: fullScreen ? "100%" : "500px",
        },
      }}
    >
      <DialogTitle
        className="modal-modal-title"
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: { xs: "16px", sm: "20px 24px" },
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: 600,
            color: "black",
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
          }}
        >
          Website URL
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
          className="closeBtn"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          isValid,
          dirty,
        }) => (
          <Form className="commonModal">
            <DialogContent
              sx={{
                padding: { xs: "16px", sm: "24px" },
                backgroundColor: "white",
              }}
            >
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  mb: 2,
                  color: "rgba(0, 0, 0, 0.6)",
                  fontSize: { xs: "0.875rem", sm: "1rem" },
                }}
              >
                Add your business website URL to help customers learn more about
                your business and services.{" "}
                <Link href="#" color="primary" underline="always">
                  Learn more
                </Link>
              </Typography>

              {/* Current Website Display */}
              {currentWebsiteUrl && (
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 600,
                      color: "black",
                    }}
                  >
                    Current Website
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      p: 2,
                      border: "1px solid rgba(0, 0, 0, 0.12)",
                      borderRadius: "8px",
                      backgroundColor: "rgba(0, 0, 0, 0.02)",
                    }}
                  >
                    <LanguageIcon sx={{ mr: 1, color: "text.secondary" }} />
                    <Link
                      href={currentWebsiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{
                        color: "primary.main",
                        textDecoration: "none",
                        "&:hover": { textDecoration: "underline" },
                        flex: 1,
                      }}
                    >
                      {formatUrlForDisplay(currentWebsiteUrl)}
                    </Link>
                  </Box>
                </Box>
              )}

              <Box className="commonInput">
                <TextField
                  autoFocus
                  margin="dense"
                  id="websiteUrl"
                  name="websiteUrl"
                  label="Website URL"
                  fullWidth
                  variant="outlined"
                  value={values.websiteUrl}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.websiteUrl && Boolean(errors.websiteUrl)}
                  helperText={touched.websiteUrl && errors.websiteUrl}
                  placeholder="https://www.example.com"
                  sx={{
                    mt: 2,
                    "& .MuiInputBase-input": {
                      color: "black",
                      padding: { xs: "12px 14px", sm: "16px 14px" },
                      fontSize: { xs: "0.9rem", sm: "1rem" },
                    },
                    "& .MuiFormHelperText-root": {
                      marginLeft: 0,
                    },
                  }}
                  InputProps={{
                    style: { backgroundColor: "white" },
                    startAdornment: (
                      <LanguageIcon sx={{ mr: 1, color: "text.secondary" }} />
                    ),
                  }}
                />
              </Box>

              {/* URL Format Help */}
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>URL Format Tips:</strong>
                  <br />
                  • Include the full URL with https:// or http://
                  <br />
                  • Example: https://www.yourbusiness.com
                  <br />• Make sure the website is accessible to customers
                </Typography>
              </Alert>

              {/* Website Benefits */}
              <Box sx={{ mt: 3 }}>
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 1,
                    fontWeight: 600,
                    color: "black",
                  }}
                >
                  Benefits of adding your website:
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Customers can easily find more information about your
                  business
                  <br />
                  • Increases credibility and trust
                  <br />
                  • Drives traffic to your website
                  <br />• Helps with local SEO and online visibility
                </Typography>
              </Box>
            </DialogContent>

            <DialogActions
              sx={{
                padding: { xs: "16px", sm: "16px 24px 24px" },
                justifyContent: "space-between",
                borderTop: "1px solid rgba(0, 0, 0, 0.12)",
              }}
            >
              <Box>
                {currentWebsiteUrl && (
                  <Button
                    onClick={handleRemoveWebsite}
                    color="error"
                    sx={{
                      textTransform: "none",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                    }}
                  >
                    Remove Website
                  </Button>
                )}
              </Box>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Button
                  onClick={onClose}
                  variant="outlined"
                  className="closeFillBtn"
                  sx={{
                    textTransform: "none",
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  }}
                  startIcon={<CancelOutlinedIcon />}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  className="primaryFillBtn"
                  disabled={
                    !isValid || (!dirty && !currentWebsiteUrl) || isSubmitting
                  }
                  sx={{
                    textTransform: "none",
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  }}
                >
                  {currentWebsiteUrl ? "Update" : "Add"} Website
                </Button>
              </Box>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
};

export default WebsiteUrlManagement;
