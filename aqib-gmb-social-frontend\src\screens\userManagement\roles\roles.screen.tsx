import React, {
  ChangeEvent,
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import Switch from "@mui/material/Switch";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import DeleteIcon from "@mui/icons-material/Delete";
import SendIcon from "@mui/icons-material/Send";
import Stack from "@mui/material/Stack";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Chip from "@mui/material/Chip";
import Grid from "@mui/material/Grid";

//Icons
import DriveFileRenameOutlineIcon from "@mui/icons-material/DriveFileRenameOutline";
import SecurityIcon from "@mui/icons-material/Security";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import BusinessIcon from "@mui/icons-material/Business";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import RateReviewIcon from "@mui/icons-material/RateReview";
import QuestionAnswerIcon from "@mui/icons-material/QuestionAnswer";
import PostAddIcon from "@mui/icons-material/PostAdd";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import AnalyticsIcon from "@mui/icons-material/Analytics";

//Components
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";

//Css
import "../roles/roles.screen.style.css";
import RolesService from "../../../services/roles/roles.service";
import { useDispatch, useSelector } from "react-redux";
import {
  IRole,
  IRolesResponseModel,
} from "../../../interfaces/response/IRolesResponseModel";
import { ObjectType } from "typescript";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { LoadingContext } from "../../../context/loading.context";
import { MessageConstants } from "../../../constants/message.constant";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import { RoleType } from "../../../constants/dbConstant.constant";
import { useNavigate } from "react-router-dom";
import { theme } from "../../../theme";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

type IAddEditRole = {
  show: boolean;
  role: IRole | null;
};

type IPendingChange = {
  roleId: number;
  fieldname: string;
  value: number;
};

const Roles: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [openEdit, setOpenEdit] = React.useState<IAddEditRole>({
    show: false,
    role: null,
  });
  const [pendingChanges, setPendingChanges] = useState<IPendingChange[]>([]);
  const [localRole, setLocalRole] = useState<IRole | null>(null);

  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [rolesList, setRolesList] = useState<IRole[]>([]);
  const { setToastConfig } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const _rolesService = new RolesService(dispatch);

  const getRolesList = async () => {
    try {
      setLoading(true);
      var roles: IRolesResponseModel = await _rolesService.roleList(
        userInfo.id
      );
      if (roles.list.length > 0) {
        setRolesList(roles.list);
        if (openEdit.show && openEdit.role != null) {
          const updatedRole = roles.list.filter(
            (x) => x.id === localRole?.id
          )[0];
          setOpenEdit({
            role: updatedRole,
            show: true,
          });
          setLocalRole(updatedRole);
        }
      }
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    }

    setLoading(false);
  };

  const addToPendingChanges = (
    roleId: number,
    fieldname: string,
    value: number
  ) => {
    setPendingChanges((prev) => {
      // Remove existing change for the same field if it exists
      const filtered = prev.filter(
        (change) =>
          !(change.roleId === roleId && change.fieldname === fieldname)
      );
      // Add the new change
      return [...filtered, { roleId, fieldname, value }];
    });
  };

  const updateLocalRole = (fieldname: string, value: number) => {
    if (localRole) {
      setLocalRole((prev) => ({
        ...prev!,
        [fieldname]: value,
      }));
    }
  };

  const handlePermissionChange = (fieldname: string, value: number) => {
    if (localRole?.id) {
      addToPendingChanges(openEdit.role.id, fieldname, value);
      updateLocalRole(fieldname, value);
    }
  };

  const submitAllChanges = async () => {
    if (pendingChanges.length === 0) {
      setToastConfig(ToastSeverity.Info, "No changes to save.", true);
      return;
    }

    setLoading(true);
    let successCount = 0;
    let errorCount = 0;

    for (const change of pendingChanges) {
      try {
        await _rolesService.updateRole(change.roleId, {
          fieldname: change.fieldname,
          value: change.value,
        });
        successCount++;
        setToastConfig(
          ToastSeverity.Success,
          `Updated ${change.fieldname} successfully.`,
          true
        );
        // Small delay to show individual toasts
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (error) {
        errorCount++;
        setToastConfig(
          ToastSeverity.Error,
          `Failed to update ${change.fieldname}.`,
          true
        );
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    // Clear pending changes after processing
    setPendingChanges([]);

    // Show summary toast
    if (successCount > 0 && errorCount === 0) {
      setToastConfig(
        ToastSeverity.Success,
        `All ${successCount} changes saved successfully!`,
        true
      );
    } else if (successCount > 0 && errorCount > 0) {
      setToastConfig(
        ToastSeverity.Warning,
        `${successCount} changes saved, ${errorCount} failed.`,
        true
      );
    } else if (errorCount > 0) {
      setToastConfig(
        ToastSeverity.Error,
        `All ${errorCount} changes failed to save.`,
        true
      );
    }

    // Refresh the roles list to get updated data
    await getRolesList();
    setLoading(false);
  };

  const handleModalOpen = (role: IRole) => {
    setOpenEdit({ show: true, role });
    setLocalRole({ ...role }); // Create a copy for local editing
    setPendingChanges([]); // Clear any pending changes
  };

  const handleModalClose = () => {
    setOpenEdit({ show: false, role: null });
    setLocalRole(null);
    setPendingChanges([]); // Clear pending changes when closing
  };

  useEffect(() => {
    if (userInfo.roleId !== RoleType.Admin) {
      navigate("/un-authorized");
    }
  }, []);

  useEffect(() => {
    getRolesList();
  }, []);

  const featuresNameText = (role: IRole) => {
    let rolesFeaturesString = "";
    const someObj: ObjectType = role as any;
    const validationKeys = [
      "BusinessManagement",
      "LocationManagement",
      "PostsManagement",
      "QandAManagement",
      "ReviewsManagement",
      "RoleManagement",
      "UserManagement",
    ];
    const orderedKeys = Object.keys(role)
      .sort()
      .reduce((obj: any, key) => {
        obj[key as keyof ObjectType] = someObj[key as keyof ObjectType];
        return obj;
      }, {});
    Object.keys(orderedKeys).forEach(function (key: string) {
      if (
        validationKeys.includes(key) &&
        someObj[key as keyof ObjectType] === 1
      ) {
        switch (key) {
          case "Analytics":
            rolesFeaturesString += ", Analytics";
            break;
          case "BusinessCreate":
            rolesFeaturesString += ", Business Create";
            break;
          case "BusinessDelete":
            rolesFeaturesString += ", Business Delete";
            break;
          case "BusinessEdit":
            rolesFeaturesString += ", Business Edit";
            break;
          case "BusinessManagement":
            rolesFeaturesString += ", Business Management";
            break;
          case "CallBack":
            rolesFeaturesString += ", CallBack";
            break;
          case "Dashboard":
            rolesFeaturesString += ", Dashboard";
            break;
          case "LocationCreate":
            rolesFeaturesString += ", Location Create";
            break;
          case "LocationManagement":
            rolesFeaturesString += ", Location Management";
            break;
          case "LocationRefresh":
            rolesFeaturesString += ", Location Refresh";
            break;
          case "PostsCreate":
            rolesFeaturesString += ", Posts Create";
            break;
          case "PostsDelete":
            rolesFeaturesString += ", Posts Delete";
            break;
          case "PostsEdit":
            rolesFeaturesString += ", Posts Edit";
            break;
          case "PostsManagement":
            rolesFeaturesString += ", Posts Management";
            break;
          case "QandAManagement":
            rolesFeaturesString += ", Q&A Management";
            break;
          case "QandARefresh":
            rolesFeaturesString += ", QandA Refresh";
            break;
          case "QandAReply":
            rolesFeaturesString += ", QandA Reply";
            break;
          case "ReviewsManagement":
            rolesFeaturesString += ", Reviews Management";
            break;
          case "ReviewsRefresh":
            rolesFeaturesString += ", Reviews Refresh";
            break;
          case "ReviewsReply":
            rolesFeaturesString += ", Reviews Reply";
            break;
          case "RoleManagement":
            rolesFeaturesString += ", Role Management";
            break;
          case "UserCreate":
            rolesFeaturesString += ", User Create";
            break;
          case "UserDelete":
            rolesFeaturesString += ", User Delete";
            break;
          case "UserEdit":
            rolesFeaturesString += ", User Edit";
            break;
          case "UserManagement":
            rolesFeaturesString += ", User Management";
            break;
        }
      }
    });

    return rolesFeaturesString.substring(1);
  };

  const updateRole = async (
    roleId: number | undefined,
    fieldname: string,
    value: number
  ) => {
    setLoading(true);
    try {
      await _rolesService.updateRole(roleId, {
        fieldname,
        value,
      });
      setToastConfig(ToastSeverity.Success, "Updated Successfully.", true);

      getRolesList();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    }

    setLoading(false);
  };

  const getIntegerFromBoolean = (value: boolean) => (value ? 1 : 0);

  const getClassNameOnRoleId = (roleId: number) => {
    switch (roleId) {
      case RoleType.Admin:
        return "hightlightBox admin";
      case RoleType.Manager:
        return "hightlightBox manager";
      case RoleType.User:
        return "hightlightBox user";
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              <Box className="commonTableHeader">
                <h3 className="pageTitle">Role Management</h3>
              </Box>
              <Box>
                <TableContainer className="commonTable">
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Names/Permissions</TableCell>
                        <TableCell align="right">Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {rolesList.length > 0 &&
                        rolesList.map((role: IRole) => (
                          <TableRow>
                            <TableCell
                              scope="row"
                              className="gridResponsiveTextLeft"
                            >
                              <Typography className="tablePrimaryText hightlightBoxText">
                                <span
                                  className={getClassNameOnRoleId(role.id)}
                                ></span>
                                {role.role}
                              </Typography>
                              <Typography className="tableSecondaryText">
                                {featuresNameText(role)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right" data-label="Status">
                              <FormControlLabel
                                control={
                                  <Switch
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) => {
                                      updateRole(
                                        role.id,
                                        "IsActive",
                                        getIntegerFromBoolean(checked)
                                      );
                                    }}
                                    checked={Boolean(role.IsActive)}
                                  />
                                }
                                label=""
                              />
                            </TableCell>
                            <TableCell align="right" data-label="Actions">
                              <Box className="commonTableActionBtns">
                                <Box>
                                  <Button
                                    variant="outlined"
                                    className="emptyBtn editIconBtn"
                                    startIcon={<DriveFileRenameOutlineIcon />}
                                    onClick={() => handleModalOpen(role)}
                                  ></Button>
                                </Box>
                                {/* <Box>
                                  <Button
                                    variant="outlined"
                                    className="emptyBtn"
                                    startIcon={<DeleteOutlineIcon />}
                                  >
                                    Delete
                                  </Button>
                                </Box> */}
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>

      <GenericDrawer
        component={
          <Box className="commonModal rolePermissionsModal">
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              <span>{openEdit && openEdit.role && openEdit.role.role}</span>
              <span className="modal-sub-title">Permissions</span>
            </Typography>
            {openEdit && openEdit.role != null && localRole && (
              <Box
                id="modal-modal-description"
                className="modal-modal-description"
              >
                <Grid container spacing={3}>
                  {/* User Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <SecurityIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              User Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.UserManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "UserCreate",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "UserEdit",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "UserDelete",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "UserManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.UserManagement)
                                    }
                                    checked={Boolean(localRole?.UserCreate)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "UserCreate",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Create Users"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.UserManagement)
                                    }
                                    checked={Boolean(localRole?.UserEdit)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "UserEdit",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Edit Users"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.UserManagement)
                                    }
                                    checked={Boolean(localRole?.UserDelete)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "UserDelete",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Delete Users"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Business Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <BusinessIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Business Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.BusinessManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "BusinessCreate",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "BusinessEdit",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "BusinessDelete",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "BusinessManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.BusinessManagement)
                                    }
                                    checked={Boolean(localRole?.BusinessCreate)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "BusinessCreate",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Create Business"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.BusinessManagement)
                                    }
                                    checked={Boolean(localRole?.BusinessEdit)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "BusinessEdit",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Edit Business"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.BusinessManagement)
                                    }
                                    checked={Boolean(localRole?.BusinessDelete)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "BusinessDelete",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Delete Business"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Location Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <LocationOnIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Location Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.LocationManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "LocationCreate",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "LocationEdit",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "LocationRefresh",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "LocationManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.LocationManagement)
                                    }
                                    checked={Boolean(localRole?.LocationCreate)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "LocationCreate",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Create Locations"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.LocationManagement)
                                    }
                                    checked={Boolean(localRole?.LocationEdit)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "LocationEdit",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Edit Locations"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.LocationManagement)
                                    }
                                    checked={Boolean(
                                      localRole?.LocationRefresh
                                    )}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "LocationRefresh",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Refresh Locations"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Reviews Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <RateReviewIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Reviews Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.ReviewsManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "ReviewsRefresh",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "ReviewsReply",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "ReviewSettings",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "ReviewsManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.ReviewsManagement)
                                    }
                                    checked={Boolean(localRole?.ReviewsRefresh)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "ReviewsRefresh",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Refresh Reviews"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.ReviewsManagement)
                                    }
                                    checked={Boolean(localRole?.ReviewsReply)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "ReviewsReply",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Reply to Reviews"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.ReviewsManagement)
                                    }
                                    checked={Boolean(localRole?.ReviewSettings)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "ReviewSettings",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Review Settings"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Q&A Management Section */}
                  {/* <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <QuestionAnswerIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Q&A Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.QandAManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "QandARefresh",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "QandAReply",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "QandAManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.QandAManagement)
                                    }
                                    checked={Boolean(
                                      localRole?.QandARefresh
                                    )}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange("QandARefresh", getIntegerFromBoolean(checked))
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Refresh Q&A"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.QandAManagement)
                                    }
                                    checked={Boolean(localRole?.QandAReply)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange("QandAReply", getIntegerFromBoolean(checked))
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Reply to Q&A"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid> */}

                  {/* Posts Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <PostAddIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Posts Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.PostsManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "PostsCreate",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "PostsEdit",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "PostsDelete",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "PostsManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.PostsCreate)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "PostsCreate",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Create Posts"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.PostsEdit)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "PostsEdit",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Edit Posts"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.PostsDelete)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "PostsDelete",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Delete Posts"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                          {/* Social Platforms */}
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.Google)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "Google",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Google"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.Facebook)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "Facebook",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Facebook/ Instagram"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.LinkedIn)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "LinkedIn",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="LinkedIn"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.PostsManagement)
                                    }
                                    checked={Boolean(localRole?.Twitter)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "Twitter",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Twitter"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Asset Management Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <PhotoLibraryIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Asset Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.AssetManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "AssetCreate",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "AssetView",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "AssetDelete",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "AssetManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.AssetManagement)
                                    }
                                    checked={Boolean(localRole?.AssetCreate)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "AssetCreate",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Create/Edit Assets"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.AssetManagement)
                                    }
                                    checked={Boolean(localRole?.AssetView)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "AssetView",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="View Assets"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={4}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.AssetManagement)
                                    }
                                    checked={Boolean(localRole?.AssetDelete)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "AssetDelete",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Delete Assets"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Analytics & Reports Section */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <AnalyticsIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Analytics & Reports
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(
                                  localRole?.AnalyticsManagement
                                )}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "Analytics",
                                      getIntegerFromBoolean(checked)
                                    );
                                    handlePermissionChange(
                                      "Reports",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "AnalyticsManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.AnalyticsManagement)
                                    }
                                    checked={Boolean(localRole?.Analytics)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "Analytics",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="View Analytics"
                                className="permissionCheckbox"
                              />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.AnalyticsManagement)
                                    }
                                    checked={Boolean(localRole?.Reports)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "Reports",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="View Reports"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Geo Grid */}
                  <Grid item xs={12} md={12}>
                    <Card className="permissionCard">
                      <CardContent>
                        <Box className="permissionHeader">
                          <Box className="permissionHeaderLeft">
                            <AnalyticsIcon className="permissionIcon" />
                            <Typography className="permissionTitle">
                              Geo-Grid Management
                            </Typography>
                          </Box>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={Boolean(localRole?.GeoGridManagement)}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>,
                                  checked: boolean
                                ) => {
                                  if (!checked) {
                                    handlePermissionChange(
                                      "GeoGridScan",
                                      getIntegerFromBoolean(checked)
                                    );
                                  }
                                  handlePermissionChange(
                                    "GeoGridManagement",
                                    getIntegerFromBoolean(checked)
                                  );
                                }}
                                color="secondary"
                                size="small"
                              />
                            }
                            label=""
                            className="permissionToggle"
                          />
                        </Box>
                        <Box className="permissionContent">
                          <Grid
                            container
                            spacing={2}
                            className="permissionCheckboxGrid"
                          >
                            <Grid item xs={12} sm={6}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    disabled={
                                      !Boolean(localRole?.GeoGridManagement)
                                    }
                                    checked={Boolean(localRole?.GeoGridScan)}
                                    onChange={(
                                      event: ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) =>
                                      handlePermissionChange(
                                        "GeoGridScan",
                                        getIntegerFromBoolean(checked)
                                      )
                                    }
                                    color="primary"
                                    size="small"
                                  />
                                }
                                label="Scan"
                                className="permissionCheckbox"
                              />
                            </Grid>
                          </Grid>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            <Box>
              <Stack
                direction="row"
                className="commonFooter"
                sx={{ justifyContent: "space-between" }}
              >
                <Button
                  variant="outlined"
                  className="closeFillBtn"
                  onClick={handleModalClose}
                  startIcon={<CancelOutlinedIcon />}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  className="formPostShapeBtn"
                  startIcon={<SendIcon />}
                  onClick={submitAllChanges}
                  disabled={pendingChanges.length === 0}
                >
                  Submit Changes ({pendingChanges.length})
                </Button>
              </Stack>
            </Box>
          </Box>
        }
        isShow={openEdit.show}
        callback={handleModalClose}
      />
    </div>
  );
};

export default Roles;
