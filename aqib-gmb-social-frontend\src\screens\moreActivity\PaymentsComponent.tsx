import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Link,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Formik, Form } from "formik";
import * as yup from "yup";

// Define the form values type
interface PaymentsFormValues {
  acceptsCheques: string;
  acceptsGooglePay: string;
}

interface PaymentsProps {
  onSave?: (values: PaymentsFormValues) => void;
  initialValues?: PaymentsFormValues;
}

const Payments: React.FC<PaymentsProps> = ({ onSave, initialValues }) => {
  // State for modal
  const [isOpen, setIsOpen] = useState(false);
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));

  // Default initial values
  const defaultValues: PaymentsFormValues = {
    acceptsCheques: "",
    acceptsGooglePay: "",
  };

  // Validation schema
  const paymentsSchema = yup.object({
    acceptsCheques: yup.string().required("Please select an option"),
    acceptsGooglePay: yup.string().required("Please select an option"),
  });

  // Handle form submission
  const handleSubmit = (values: PaymentsFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Payment options saved:", values);
    setIsOpen(false);
  };

  // Payment options
  const paymentOptions = [
    {
      id: "acceptsCheques",
      label: "Accepts cheques",
    },
    {
      id: "acceptsGooglePay",
      label: "Accepts Google Pay",
    },
  ];

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        sx={{ textTransform: "none" }}
      >
        Payments
      </Button>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullWidth
        maxWidth="sm"
        fullScreen={fullScreen}
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
            maxWidth: fullScreen ? "100%" : "500px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: { xs: "16px", sm: "20px 24px" },
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
              fontSize: { xs: "1.1rem", sm: "1.25rem" },
            }}
          >
            Payments
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={() => setIsOpen(false)}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <Formik
          initialValues={initialValues || defaultValues}
          validationSchema={paymentsSchema}
          onSubmit={handleSubmit}
        >
          {({
            values,
            handleChange,
            handleSubmit,
            errors,
            touched,
            isValid,
            dirty,
          }) => (
            <Form onSubmit={handleSubmit}>
              <DialogContent
                sx={{
                  padding: { xs: "16px", sm: "24px" },
                  bgcolor: "white",
                }}
              >
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline",
                      fontSize: { xs: "0.875rem", sm: "1rem" },
                    }}
                  >
                    Let customers know more about your business by showing
                    attributes on your Business Profile. These may appear
                    publicly on Search, Maps and other Google services.{" "}
                  </Typography>
                  <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link>
                </Box>

                <Box sx={{ mb: 4 }}>
                  {paymentOptions.map((option) => (
                    <Box key={option.id} sx={{ mb: 3 }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          color: "black",
                          mb: 1,
                          fontWeight: 500,
                          fontSize: { xs: "0.9rem", sm: "1rem" },
                        }}
                      >
                        {option.label}
                      </Typography>

                      <RadioGroup
                        name={option.id}
                        value={values[option.id as keyof PaymentsFormValues]}
                        onChange={handleChange}
                        row
                      >
                        <FormControlLabel
                          value="Yes"
                          control={
                            <Radio
                              sx={{
                                color: "#1976d2",
                                "&.Mui-checked": {
                                  color: "#1976d2",
                                },
                              }}
                            />
                          }
                          label={
                            <Typography
                              sx={{
                                color: "black",
                                fontSize: { xs: "0.875rem", sm: "1rem" },
                              }}
                            >
                              Yes
                            </Typography>
                          }
                          sx={{ mr: 4 }}
                        />
                        <FormControlLabel
                          value="No"
                          control={
                            <Radio
                              sx={{
                                color: "#1976d2",
                                "&.Mui-checked": {
                                  color: "#1976d2",
                                },
                              }}
                            />
                          }
                          label={
                            <Typography
                              sx={{
                                color: "black",
                                fontSize: { xs: "0.875rem", sm: "1rem" },
                              }}
                            >
                              No
                            </Typography>
                          }
                        />
                      </RadioGroup>

                      {errors[option.id as keyof PaymentsFormValues] &&
                        touched[option.id as keyof PaymentsFormValues] && (
                          <Typography color="error" variant="caption">
                            {errors[option.id as keyof PaymentsFormValues]}
                          </Typography>
                        )}
                    </Box>
                  ))}
                </Box>
              </DialogContent>

              <DialogActions
                sx={{
                  padding: { xs: "16px", sm: "16px 24px 24px" },
                  justifyContent: "space-between",
                  borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                }}
              >
                <Button
                  onClick={() => setIsOpen(false)}
                  variant="outlined"
                  sx={{
                    textTransform: "none",
                    color: "#1976d2",
                    borderColor: "#e0e0e0",
                    bgcolor: "white",
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  }}
                  startIcon={<CancelOutlinedIcon />}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={!isValid || !dirty}
                  sx={{
                    textTransform: "none",
                    bgcolor: "#1976d2",
                    color: "white",
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  }}
                >
                  Save
                </Button>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </Dialog>
    </>
  );
};

export default Payments;
