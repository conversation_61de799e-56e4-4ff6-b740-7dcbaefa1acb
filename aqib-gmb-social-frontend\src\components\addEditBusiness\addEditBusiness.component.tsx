import { useContext, useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import UserService from "../../services/user/user.service";
import * as yup from "yup";
import { Formik } from "formik";
import { ToastContext } from "../../context/toast.context";
import { FormHelperText } from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { IRole } from "../../interfaces/response/IRolesResponseModel";
import { useDispatch, useSelector } from "react-redux";
import InputLabel from "@mui/material/InputLabel";
import FormControl from "@mui/material/FormControl";
import BusinessService from "../../services/business/business.service";
import { IBusiness } from "../../interfaces/response/IBusinessListResponseModel";
import { IBusinessGroup } from "../../interfaces/response/IBusinessGroupsResponseModel";
import { ILocation } from "../../interfaces/response/ILocationsListResponseModel";
import { LoadingContext } from "../../context/loading.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";
import {
  IUser,
  IUsersListResponse,
} from "../../interfaces/response/IUsersListResponse";
import { IAddBusinessRequestModel } from "../../interfaces/request/IAddBusinessRequestModel";
import { IBusinessCreationResponseModel } from "../../interfaces/response/IBusinessCreationResponseModel";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

const AddEditBusinessComponent = (props: {
  businessId: number;
  editData: IBusiness | null | undefined;
  callBack: (
    editData: IAddBusinessRequestModel | undefined,
    response: IBusinessCreationResponseModel | undefined
  ) => null | void | undefined;
}) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setLoading } = useContext(LoadingContext);
  const _userService = new UserService(dispatch);
  const { setToastConfig } = useContext(ToastContext);
  const [usersList, setUsersList] = useState<IUser[]>([]);

  const INITIAL_VALUES: IAddBusinessRequestModel = {
    userId: props.businessId > 0 && props.editData ? props.editData.userId : 0,
    businessName:
      props.businessId > 0 && props.editData ? props.editData.businessName : "",
    businessEmail:
      props.businessId > 0 && props.editData
        ? props.editData.businessEmail
        : "",
    statusId: 1,
    createdBy: userInfo.id,
    updatedBy: userInfo.id,
  };
  const [initialValues, setInitialValues] =
    useState<IAddBusinessRequestModel>(INITIAL_VALUES);
  useEffect(() => {}, []);
  const _businessService = new BusinessService(dispatch);

  const BusinessSchema = yup.object().shape({
    businessName: yup.string().required("This field is required"),
    businessEmail: yup
      .string()
      .email("Please enter a valid email")
      .required("This field is required"),
    createdBy: yup.number().required("This field is required"),
    statusId: yup.number().required("This field is required"),
    updatedBy: yup.number().required("This field is required"),
    // userId: yup
    //   .number()
    //   .moreThan(0, "Select valid User")
    //   .required("This field is required"),
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const users: IUsersListResponse = await _userService.getUsers(
        userInfo.id
      );
      console.log("Users: ", users.list);
      setUsersList(users.list);
    } catch (error) {}

    setLoading(false);
  };

  const _handleSaveAccount = async (values: IAddBusinessRequestModel) => {
    try {
      const isValid = await BusinessSchema.isValid(values);
      if (isValid) {
        if (props.businessId && props.businessId > 0) {
          await _businessService.updateBusiness(values, props.businessId);
          setToastConfig(
            ToastSeverity.Success,
            MessageConstants.BusinessUpdatedSuccessfully,
            true
          );
          props.callBack(undefined, undefined);
        } else {
          var response: IBusinessCreationResponseModel =
            await _businessService.addBusiness(values);
          if (response && response.response.insertId > 0) {
            props.callBack(values, response);
          }
        }
      }
    } catch (error: any) {}
  };

  return (
    <Formik
      enableReinitialize
      initialValues={{ ...initialValues }}
      validationSchema={BusinessSchema}
      onSubmit={(values) => {
        _handleSaveAccount(values);
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleSubmit,
        setFieldValue,
        handleReset,
        /* and other goodies */
      }) => (
        <form
          onSubmit={handleSubmit}
          onReset={handleReset}
          className="commonModal"
        >
          <Box className="height100">
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              {props.businessId > 0 ? "Edit" : "Add"} Business
            </Typography>

            <Box
              id="modal-modal-description"
              className="modal-modal-description"
            >
              <Box>
                <Box className="commonInput">
                  <TextField
                    id="businessName"
                    label="Business Name"
                    variant="outlined"
                    fullWidth
                    onChange={handleChange}
                    value={values.businessName}
                  />
                  {errors.businessName && touched.businessName && (
                    <FormHelperText className="errorMessage">
                      {errors.businessName}
                    </FormHelperText>
                  )}
                </Box>
                <Box className="commonInput">
                  <TextField
                    id="businessEmail"
                    label="Business Email"
                    variant="outlined"
                    fullWidth
                    onChange={handleChange}
                    value={values.businessEmail}
                  />
                  {errors.businessEmail && touched.businessEmail && (
                    <FormHelperText className="errorMessage">
                      {errors.businessEmail}
                    </FormHelperText>
                  )}
                </Box>

                {/* <Box className="commonInput">
                  <FormControl variant="outlined" fullWidth>
                    <InputLabel id="outlined-country-dropdown-label">
                      User
                    </InputLabel>

                    <Select
                      fullWidth
                      id="userId"
                      label="Select User"
                      value={values.userId.toString()}
                      onChange={(evt: SelectChangeEvent) => {
                        setFieldValue("userId", +evt.target.value);
                      }}
                      sx={{
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      }}
                    >
                      <MenuItem value={0}>Select</MenuItem>
                      {usersList &&
                        usersList.map((user: IUser) => (
                          <MenuItem key={user.id} value={user.id.toString()}>
                            {user.name}
                          </MenuItem>
                        ))}
                    </Select>
                    {errors.userId && touched.userId && (
                      <FormHelperText className="errorMessage">
                        {errors.userId}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box> */}
              </Box>
            </Box>

            <Box className="">
              <Stack direction="row" className="commonFooter">
                <Button
                  variant="outlined"
                  className="closeFillBtn"
                  onClick={() => props.callBack(undefined, undefined)}
                  startIcon={<CancelOutlinedIcon />}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  className="primaryFillBtn"
                  type="submit"
                >
                  Save Business
                </Button>
              </Stack>
            </Box>
          </Box>
        </form>
      )}
    </Formik>
  );
};

export default AddEditBusinessComponent;
