const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  createUser,
  userList,
  deleteUser,
  updateUser,
  createAssignUserLocation,
  userLocationList,
  updateUserLocation,
  enableDisableUser,
  userListpaginated,
  updatePassword,
  updateUserCombined,
} = require("../controllers/user.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - roleId
 *         - name
 *         - mobile
 *         - email
 *         - password
 *         - statusId
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the user
 *         roleId:
 *           type: integer
 *           description: Role ID of the user
 *         name:
 *           type: string
 *           description: User's full name
 *         mobile:
 *           type: string
 *           description: User's mobile number
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         mobileVerified:
 *           type: integer
 *           description: Whether mobile is verified (1) or not (0)
 *         emailVerified:
 *           type: integer
 *           description: Whether email is verified (1) or not (0)
 *         password:
 *           type: string
 *           description: User's password
 *         statusId:
 *           type: integer
 *           description: Status of the user account
 */

/**
 * @swagger
 * /v1/user:
 *   get:
 *     summary: User welcome page
 *     description: Returns a welcome message for the User API
 *     tags: [User]
 *     responses:
 *       200:
 *         description: Welcome message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Home Page
 */
router.get("/", welcome);

/**
 * @swagger
 * /v1/user/user-list/{userId}:
 *   get:
 *     summary: Get list of users
 *     description: Retrieves a list of all users for a specific user ID
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user requesting the list
 *     responses:
 *       201:
 *         description: List of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User List!
 *                 list:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *       404:
 *         description: Users not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/user-list/:userId", isAuthenticated, userList);

/**
 * @swagger
 * /v1/user/user-list:
 *   get:
 *     summary: Get paginated list of users
 *     description: Retrieves a paginated list of users
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: pageNo
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: userId
 *         schema:
 *           type: integer
 *         description: ID of the user requesting the list
 *     responses:
 *       201:
 *         description: Paginated list of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User List!
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 totalRecords:
 *                   type: integer
 *       404:
 *         description: Users not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/user-list", isAuthenticated, userListpaginated);

/**
 * @swagger
 * /v1/user/create-user:
 *   post:
 *     summary: Create a new user
 *     description: Creates a new user with the provided information
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roleId
 *               - name
 *               - mobile
 *               - email
 *               - password
 *               - statusId
 *             properties:
 *               roleId:
 *                 type: integer
 *               name:
 *                 type: string
 *                 minLength: 3
 *               mobile:
 *                 type: string
 *                 pattern: ^(\\+91|91|0)?[6789]\\d{9}$
 *               email:
 *                 type: string
 *                 format: email
 *               mobileVerified:
 *                 type: integer
 *                 default: 0
 *               emailVerified:
 *                 type: integer
 *                 default: 0
 *               password:
 *                 type: string
 *                 minLength: 3
 *               statusId:
 *                 type: integer
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Created!
 *                 response:
 *                   type: object
 *                 userId:
 *                   type: integer
 *       404:
 *         description: User not created
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create-user",
  isAuthenticated,
  validateSchema("user", "createUser"),
  createUser
);

/**
 * @swagger
 * /v1/user/assign-user:
 *   post:
 *     summary: Assign location to user
 *     description: Assigns a location to a user
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - locationId
 *             properties:
 *               userId:
 *                 type: integer
 *               locationId:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Location assigned successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Location Created!
 *                 response:
 *                   type: object
 *       404:
 *         description: Assignment failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/assign-user", isAuthenticated, createAssignUserLocation);

/**
 * @swagger
 * /v1/user/user-locationlist/{id}:
 *   get:
 *     summary: Get user's assigned locations
 *     description: Retrieves a list of locations assigned to a user
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     responses:
 *       201:
 *         description: List of user's locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Location List!
 *                 list:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       userId:
 *                         type: integer
 *                       locationId:
 *                         type: integer
 *                       locationName:
 *                         type: string
 *       404:
 *         description: Locations not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/user-locationlist/:id", isAuthenticated, userLocationList);

/**
 * @swagger
 * /v1/user/edit-user/{id}:
 *   put:
 *     summary: Update user information
 *     description: Updates a user's information
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               roleId:
 *                 type: integer
 *               name:
 *                 type: string
 *               mobile:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               mobileVerified:
 *                 type: integer
 *               emailVerified:
 *                 type: integer
 *               password:
 *                 type: string
 *               statusId:
 *                 type: integer
 *     responses:
 *       201:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put("/edit-user/:id", isAuthenticated, updateUser);

/**
 * @swagger
 * /v1/user/edit-userlocation:
 *   put:
 *     summary: Update user-location assignment
 *     description: Updates the assignment of a location to a user
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - userId
 *               - locationId
 *             properties:
 *               id:
 *                 type: integer
 *               userId:
 *                 type: integer
 *               locationId:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Assignment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Location Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put("/edit-userlocation", isAuthenticated, updateUserLocation);

/**
 * @swagger
 * /v1/user/enable-disable/{id}:
 *   post:
 *     summary: Enable or disable user
 *     description: Enables or disables a user account
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user to enable/disable
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - isActive
 *             properties:
 *               isActive:
 *                 type: integer
 *                 description: 1 for active, 0 for inactive
 *     responses:
 *       201:
 *         description: User status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/enable-disable/:id", isAuthenticated, enableDisableUser);

/**
 * @swagger
 * /v1/user/delete-user/{id}:
 *   delete:
 *     summary: Delete user
 *     description: Deletes a user account
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user to delete
 *     responses:
 *       201:
 *         description: User deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Deleted!
 *                 list:
 *                   type: object
 *       404:
 *         description: Deletion failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete("/delete-user/:id", isAuthenticated, deleteUser);

/**
 * @swagger
 * /v1/user/update-password/{id}:
 *   put:
 *     summary: Update user password
 *     description: Updates a user's password
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 minLength: 3
 *     responses:
 *       201:
 *         description: Password updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Updated!
 *                 list:
 *                   type: object
 *       404:
 *         description: Update failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put("/update-password/:id", isAuthenticated, updatePassword);

/**
 * @swagger
 * /v1/user/update-user-combined:
 *   put:
 *     summary: Combined user update
 *     description: Updates user information, password, and location assignments in a single request
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - statusId
 *               - mobile
 *               - roleId
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: ID of the user to update
 *               roleId:
 *                 type: integer
 *                 description: Role ID for the user
 *               name:
 *                 type: string
 *                 description: User's full name
 *               mobile:
 *                 type: string
 *                 pattern: ^(\\+91|91|0)?[6789]\\d{9}$
 *                 description: User's mobile number
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               mobileVerified:
 *                 type: integer
 *                 description: Whether mobile is verified (1) or not (0)
 *               emailVerified:
 *                 type: integer
 *                 description: Whether email is verified (1) or not (0)
 *               statusId:
 *                 type: integer
 *                 description: Status of the user account
 *               password:
 *                 type: string
 *                 minLength: 3
 *                 description: New password for the user
 *               confirmPassword:
 *                 type: string
 *                 minLength: 3
 *                 description: Confirmation of the new password
 *               locationId:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of location IDs to assign to the user
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User updated successfully
 *                 userUpdate:
 *                   type: object
 *                 locationUpdate:
 *                   type: string
 *       400:
 *         description: Invalid request data or password mismatch
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update-user-combined",
  isAuthenticated,
  validateSchema("user", "updateUserCombined"),
  updateUserCombined
);

module.exports = router;
