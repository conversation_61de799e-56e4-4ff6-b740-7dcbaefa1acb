import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  <PERSON>ton,
  Typography,
  Box,
  Chip,
} from "@mui/material";
import { GroupWork, Edit } from "@mui/icons-material";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

interface BulkPostEditDialogProps {
  open: boolean;
  onClose: () => void;
  onEditSingle: () => void;
  onEditAll: () => void;
  totalPosts: number;
  bulkPostId: string;
}

const BulkPostEditDialog: React.FC<BulkPostEditDialogProps> = ({
  open,
  onClose,
  onEditSingle,
  onEditAll,
  totalPosts,
  bulkPostId,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <GroupWork color="primary" />
          <Typography variant="h6" component="span">
            Bulk Post Detected
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ py: 1 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            This post is part of a bulk post containing{" "}
            <Chip
              label={`${totalPosts} posts`}
              color="primary"
              size="small"
              sx={{ mx: 0.5 }}
            />
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Would you like to edit just this individual post or all posts in the
            bulk group?
          </Typography>

          <Box
            sx={{
              p: 2,
              backgroundColor: "grey.50",
              borderRadius: 1,
              border: "1px solid",
              borderColor: "grey.200",
            }}
          >
            <Typography variant="caption" color="text.secondary">
              Bulk Post ID: {bulkPostId}
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1, justifyContent: "space-between" }}>
        <Button
          onClick={onClose}
          variant="outlined"
          color="secondary"
          startIcon={<CancelOutlinedIcon />}
          className="closeFillBtn"
        >
          Cancel
        </Button>

        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={onEditSingle}
            variant="outlined"
            color="primary"
            startIcon={<Edit />}
          >
            Edit This Post Only
          </Button>

          <Button
            onClick={onEditAll}
            variant="contained"
            color="primary"
            startIcon={<GroupWork />}
          >
            Edit All {totalPosts} Posts
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default BulkPostEditDialog;
