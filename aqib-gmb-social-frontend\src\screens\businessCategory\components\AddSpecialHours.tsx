import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  IconButton,
  TextField,
  Divider,
  Grid,
  Link,
  Paper,
  Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { Formik, Form, FieldArray } from "formik";
import * as yup from "yup";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

// Define the special hour type
interface SpecialHour {
  id: string;
  name: string;
  date: dayjs.Dayjs | null;
  isOpen: boolean;
  openTime?: string;
  closeTime?: string;
}

// Define the form values type
interface SpecialHoursFormValues {
  specialHours: SpecialHour[];
}

interface SpecialHoursProps {
  onSave?: (values: SpecialHoursFormValues) => void;
  initialValues?: SpecialHoursFormValues;
}

const AddSpecialHours: React.FC<SpecialHoursProps> = ({
  onSave,
  initialValues,
}) => {
  // State for modal
  const [isOpen, setIsOpen] = useState(false);

  // Default initial values
  const defaultValues: SpecialHoursFormValues = {
    specialHours: [
      {
        id: "1",
        name: "Eid al-Adha",
        date: dayjs("2025-06-06"),
        isOpen: true,
        openTime: "10:00",
        closeTime: "13:00",
      },
      {
        id: "2",
        name: "Eid al-Adha",
        date: dayjs("2025-06-07"),
        isOpen: true,
        openTime: "10:00",
        closeTime: "13:00",
      },
      {
        id: "3",
        name: "Ashura",
        date: dayjs("2025-07-05"),
        isOpen: true,
        openTime: "10:00",
        closeTime: "13:00",
      },
      {
        id: "4",
        name: "Ashura",
        date: dayjs("2025-07-06"),
        isOpen: true,
        openTime: "10:00",
        closeTime: "13:00",
      },
    ],
  };

  // Validation schema
  const specialHoursSchema = yup.object({
    specialHours: yup.array().of(
      yup.object({
        name: yup.string().required("Holiday name is required"),
        date: yup.date().required("Date is required").nullable(),
        isOpen: yup.boolean(),
      })
    ),
  });

  // Handle form submission
  const handleSubmit = (values: SpecialHoursFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Special hours saved:", values);
    setIsOpen(false);
  };

  // Additional hour types
  const additionalHourTypes = [
    "Breakfast",
    "Delivery",
    "Dinner",
    "Drive-through",
    "Hours for the elderly",
    "Lunch",
    "Online operating hours",
    "Pick-up",
  ];

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        sx={{ textTransform: "none" }}
      >
        Special Hours
      </Button>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
            }}
          >
            Special hours
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={() => setIsOpen(false)}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 3, bgcolor: "white" }}>
          <Formik
            initialValues={initialValues || defaultValues}
            validationSchema={specialHoursSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleSubmit, setFieldValue }) => (
              <Form onSubmit={handleSubmit}>
                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline",
                    }}
                  >
                    Confirm public holidays or add hours so customers know when
                    you're open.{" "}
                  </Typography>
                  <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link>
                </Box>

                <FieldArray name="specialHours">
                  {({ push, remove }) => (
                    <Box>
                      {values.specialHours.map((hour, index) => (
                        <Box key={hour.id}>
                          <Grid
                            container
                            spacing={2}
                            sx={{ mb: 2, alignItems: "center" }}
                          >
                            <Grid item xs={12} sm={4}>
                              <Typography
                                sx={{ color: "black", fontWeight: 500 }}
                              >
                                {hour.name}
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {hour.date
                                  ? hour.date.format("D MMM YYYY")
                                  : ""}
                              </Typography>
                            </Grid>

                            <Grid
                              item
                              xs={12}
                              sm={8}
                              sx={{ textAlign: "right" }}
                            >
                              <Button
                                variant="outlined"
                                sx={{
                                  color: "#1976d2",
                                  borderColor: "#1976d2",
                                  textTransform: "none",
                                }}
                                onClick={() =>
                                  console.log(`Review ${hour.name}`)
                                }
                              >
                                Review
                              </Button>
                            </Grid>
                          </Grid>
                          {index < values.specialHours.length - 1 && (
                            <Divider sx={{ my: 2 }} />
                          )}
                        </Box>
                      ))}

                      <Box sx={{ mt: 3 }}>
                        <Button
                          startIcon={<AddIcon />}
                          sx={{
                            color: "#1976d2",
                            textTransform: "none",
                          }}
                          onClick={() => {
                            push({
                              id: String(values.specialHours.length + 1),
                              name: "",
                              date: null,
                              isOpen: true,
                            });
                          }}
                        >
                          Add a date
                        </Button>
                      </Box>
                    </Box>
                  )}
                </FieldArray>

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    mt: 4,
                    mb: 4,
                    gap: 2,
                  }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: "none",
                      bgcolor: "#1976d2",
                      color: "white",
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setIsOpen(false)}
                    className="closeFillBtn"
                    sx={{
                      textTransform: "none",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                    }}
                    startIcon={<CancelOutlinedIcon />}
                  >
                    Cancel
                  </Button>
                </Box>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" sx={{ color: "black", mb: 2 }}>
                  Add more hours
                </Typography>

                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 3 }}>
                  {additionalHourTypes.map((type) => (
                    <Chip
                      key={type}
                      icon={<AddIcon />}
                      label={type}
                      variant="outlined"
                      sx={{
                        borderRadius: "20px",
                        px: 1,
                        color: "#1976d2",
                        borderColor: "#e0e0e0",
                      }}
                      onClick={() => console.log(`Add ${type}`)}
                    />
                  ))}
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddSpecialHours;
