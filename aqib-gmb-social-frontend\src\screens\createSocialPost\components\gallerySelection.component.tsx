import React, { useState, useEffect, useContext } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Grid,
  Card,
  CardMedia,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Divider,
  Alert,
  CircularProgress,
  Chip,
  Checkbox,
} from "@mui/material";
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import ManageAssetsService from "../../../services/manageAssets/manageAssets.service";
import BusinessService from "../../../services/business/business.service";
import { FileUtils } from "../../../utils/fileUtils";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

interface IAsset {
  id: number;
  business_id: number;
  user_id: number;
  file_name: string;
  original_file_name: string;
  file_type: "image" | "video";
  file_size: number;
  s3_key: string;
  s3_url: string;
  mime_type: string;
  upload_date: string;
  status: string;
  uploaded_by_name?: string;
  thumbnail_s3_url?: string;
}

interface IBusiness {
  id: number;
  businessName: string;
}

interface GallerySelectionProps {
  open: boolean;
  onClose: () => void;
  onImageSelect: (imageUrl: string, asset: IAsset) => void;
  onComputerSelect?: () => void;
}

const GallerySelectionComponent: React.FC<GallerySelectionProps> = ({
  open,
  onClose,
  onImageSelect,
  onComputerSelect,
}) => {
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setToastConfig } = useContext(ToastContext);
  const dispatch = useDispatch();

  // Initialize services
  const manageAssetsService = new ManageAssetsService(dispatch);
  const businessService = new BusinessService(dispatch);

  // State management
  const [businesses, setBusinesses] = useState<IBusiness[]>([]);
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(
    null
  );
  const [assets, setAssets] = useState<IAsset[]>([]);
  const [selectedAsset, setSelectedAsset] = useState<IAsset | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingAssets, setLoadingAssets] = useState(false);
  const [brokenImages, setBrokenImages] = useState<Set<number>>(new Set());

  // Load businesses when component mounts
  useEffect(() => {
    if (open) {
      loadBusinesses();
    }
  }, [open]);

  // Load assets when business is selected
  useEffect(() => {
    if (selectedBusinessId) {
      loadAssets();
    } else {
      setAssets([]);
      setSelectedAsset(null);
    }
  }, [selectedBusinessId]);

  const loadBusinesses = async () => {
    try {
      setLoading(true);
      const response = await businessService.getBusiness(userInfo.id);
      if (response.list && response.list.length > 0) {
        setBusinesses(response.list);
        // Auto-select first business if only one exists
        if (response.list.length === 1) {
          setSelectedBusinessId(response.list[0].id);
        }
      }
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load businesses", true);
    } finally {
      setLoading(false);
    }
  };

  const loadAssets = async () => {
    if (!selectedBusinessId) return;

    try {
      setLoadingAssets(true);
      const response = await manageAssetsService.getAssets(
        selectedBusinessId,
        1,
        50 // Load more assets for gallery view
      );

      if (response.success) {
        // Filter images and videos for post creation
        const mediaAssets =
          response.data?.filter(
            (asset: IAsset) =>
              asset.file_type === "image" || asset.file_type === "video"
          ) || [];
        setAssets(mediaAssets);
      } else {
        setAssets([]);
      }
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load assets", true);
      setAssets([]);
    } finally {
      setLoadingAssets(false);
    }
  };

  const handleBusinessSelect = (businessId: number) => {
    setSelectedBusinessId(businessId);
    setSelectedAsset(null);
  };

  const handleAssetSelect = (asset: IAsset) => {
    setSelectedAsset(asset);
  };

  const handleConfirmSelection = () => {
    if (selectedAsset) {
      // Use thumbnail if available, otherwise use original image
      const imageUrl = selectedAsset.thumbnail_s3_url || selectedAsset.s3_url;
      onImageSelect(imageUrl, selectedAsset);
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedAsset(null);
    setSelectedBusinessId(null);
    setAssets([]);
    setBrokenImages(new Set());
    onClose();
  };

  const handleImageError = (assetId: number, asset: IAsset) => {
    console.error("Image failed to load:", {
      assetId,
      thumbnail_s3_url: asset.thumbnail_s3_url,
      s3_url: asset.s3_url,
      file_name: asset.file_name,
      original_file_name: asset.original_file_name,
    });
    setBrokenImages((prev) => new Set([...prev, assetId]));
  };

  const getPreviewUrl = (asset: IAsset): string => {
    // Use thumbnail if available, otherwise use original for images
    const url = asset.thumbnail_s3_url || asset.s3_url;

    // Debug logging
    console.log("Asset URL:", {
      assetId: asset.id,
      thumbnail_s3_url: asset.thumbnail_s3_url,
      s3_url: asset.s3_url,
      selectedUrl: url,
    });

    // Return the URL as-is since it should be a properly signed URL from the backend
    return url || "";
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: "80vh", maxHeight: "800px" },
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Select Media from Gallery</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ padding: 0 }}>
        <Grid container sx={{ height: "100%" }}>
          {/* Business Selection Sidebar */}
          <Grid
            item
            xs={12}
            md={3}
            sx={{
              borderRight: "1px solid #e0e0e0",
              backgroundColor: "#f5f5f5",
              maxHeight: "100%",
              overflow: "auto",
            }}
          >
            <Box p={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Select Business
              </Typography>
              {loading ? (
                <Box display="flex" justifyContent="center" p={2}>
                  <CircularProgress size={24} />
                </Box>
              ) : (
                <List dense>
                  {businesses.map((business) => (
                    <ListItem key={business.id} disablePadding>
                      <ListItemButton
                        selected={selectedBusinessId === business.id}
                        onClick={() => handleBusinessSelect(business.id)}
                        sx={{
                          borderRadius: 1,
                          mb: 0.5,
                          "&.Mui-selected": {
                            backgroundColor: "primary.main",
                            color: "white",
                            "&:hover": {
                              backgroundColor: "primary.dark",
                            },
                          },
                        }}
                      >
                        <ListItemText
                          primary={business.businessName}
                          primaryTypographyProps={{
                            fontSize: "0.9rem",
                            fontWeight:
                              selectedBusinessId === business.id
                                ? "bold"
                                : "normal",
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          </Grid>

          {/* Assets Gallery */}
          <Grid item xs={12} md={9}>
            <Box p={2} sx={{ height: "100%", overflow: "auto" }}>
              {!selectedBusinessId ? (
                <Alert severity="info">
                  Please select a business from the left sidebar to view media.
                </Alert>
              ) : loadingAssets ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="200px"
                >
                  <CircularProgress />
                </Box>
              ) : assets.length === 0 ? (
                <Alert severity="info">
                  No media found for this business. Upload some images or videos
                  first in the Manage Assets section.
                </Alert>
              ) : (
                <>
                  <Box
                    display="flex"
                    justifyContent="space-between"
                    alignItems="center"
                    mb={2}
                  >
                    <Typography variant="subtitle1" fontWeight="bold">
                      Select Media ({assets.length} available)
                    </Typography>
                    {selectedAsset && (
                      <Chip
                        icon={<CheckCircleIcon />}
                        label={`${
                          selectedAsset.file_type === "video"
                            ? "Video"
                            : "Image"
                        } Selected`}
                        color="success"
                        variant="outlined"
                      />
                    )}
                  </Box>
                  <Grid container spacing={2}>
                    {assets.map((asset) => (
                      <Grid item xs={6} sm={4} md={3} key={asset.id}>
                        <Card
                          sx={{
                            position: "relative",
                            cursor: "pointer",
                            border:
                              selectedAsset?.id === asset.id
                                ? "3px solid"
                                : "1px solid",
                            borderColor:
                              selectedAsset?.id === asset.id
                                ? "primary.main"
                                : "grey.300",
                            transition: "all 0.2s ease",
                            "&:hover": {
                              transform: "translateY(-2px)",
                              boxShadow: 3,
                            },
                          }}
                          onClick={() => handleAssetSelect(asset)}
                        >
                          {/* Selection Indicator */}
                          {selectedAsset?.id === asset.id && (
                            <Box
                              sx={{
                                position: "absolute",
                                top: 8,
                                right: 8,
                                zIndex: 1,
                                backgroundColor: "primary.main",
                                borderRadius: "50%",
                                padding: "4px",
                              }}
                            >
                              <CheckCircleIcon
                                sx={{ color: "white", fontSize: 20 }}
                              />
                            </Box>
                          )}

                          {brokenImages.has(asset.id) ||
                          asset.file_type === "video" ? (
                            // Fallback for broken images or video placeholder
                            <Box
                              sx={{
                                height: 120,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "#f5f5f5",
                                border: "1px dashed #ccc",
                              }}
                            >
                              <Box textAlign="center">
                                {asset.file_type === "video" ? (
                                  <>
                                    <VideoIcon
                                      sx={{
                                        fontSize: 40,
                                        color: "#666",
                                        mb: 1,
                                      }}
                                    />
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      Video
                                    </Typography>
                                  </>
                                ) : (
                                  <>
                                    <ImageIcon
                                      sx={{
                                        fontSize: 40,
                                        color: "#ccc",
                                        mb: 1,
                                      }}
                                    />
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      Image not available
                                    </Typography>
                                  </>
                                )}
                              </Box>
                            </Box>
                          ) : (
                            <CardMedia
                              component="img"
                              height="120"
                              image={getPreviewUrl(asset)}
                              alt={asset.original_file_name}
                              onError={() => handleImageError(asset.id, asset)}
                              sx={{
                                objectFit: "cover",
                                backgroundColor: "#f5f5f5",
                              }}
                            />
                          )}
                          <Box p={1}>
                            <Typography
                              variant="caption"
                              display="block"
                              sx={{
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "nowrap",
                              }}
                            >
                              {asset.original_file_name}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {FileUtils.formatFileSize(asset.file_size)}
                            </Typography>
                          </Box>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ justifyContent: "space-between", padding: 2 }}>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={handleClose}
            className="closeFillBtn"
            variant="outlined"
            startIcon={<CancelOutlinedIcon />}
          >
            Cancel
          </Button>
          {onComputerSelect && (
            <Button
              onClick={() => {
                onComputerSelect();
                handleClose();
              }}
              variant="outlined"
              className="formPostShapeBtn"
            >
              Select from Computer
            </Button>
          )}
        </Box>
        <Button
          onClick={handleConfirmSelection}
          variant="contained"
          disabled={!selectedAsset}
          sx={{ minHeight: "46px", borderRadius: "6px !important" }}
          className="formPostShapeBtn"
        >
          Use Selected Media
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GallerySelectionComponent;
