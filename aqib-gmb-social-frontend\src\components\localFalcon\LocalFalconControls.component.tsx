import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Autocomplete,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Card,
  CardContent,
  Stack,
  Alert,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";
import {
  LocalFalconConfiguration,
  LocalFalconSearchRequest,
  LocalFalconScanRequest,
  LocalFalconBusiness,
} from "../../services/localFalcon/localFalcon.service";
import KeywordSuggestions from "./KeywordSuggestions.component";
import GooglePlacesService, {
  ServiceAreaSuggestion,
} from "../../services/googlePlaces/googlePlaces.service";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";

interface LocalFalconControlsProps {
  onSearch: (searchRequest: LocalFalconSearchRequest) => void;
  onScan: (scanRequest: LocalFalconScanRequest) => void;
  onSaveConfiguration: () => void;
  loading: boolean;
  currentLocation: any;
  savedConfigurations: LocalFalconConfiguration[];
  onLoadConfiguration: (config: LocalFalconConfiguration) => void;
  onDeleteConfiguration: (configId: number) => void;
  configuration: LocalFalconConfiguration;
  onConfigurationChange: (updates: Partial<LocalFalconConfiguration>) => void;
  localFalconService: any; // Add service for API calls
}

const LocalFalconControls: React.FC<LocalFalconControlsProps> = ({
  onScan,
  onSaveConfiguration,
  loading,
  currentLocation,
  savedConfigurations,
  onLoadConfiguration,
  onDeleteConfiguration,
  configuration,
  onConfigurationChange,
  localFalconService,
}) => {
  const [selectedBusiness, setSelectedBusiness] =
    useState<LocalFalconBusiness | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [configToDelete, setConfigToDelete] = useState<number | null>(null);

  const [businessLoading, setBusinessLoading] = useState(false);
  const [googlePlacesService] = useState(() => new GooglePlacesService());
  const [placeSuggestions, setPlaceSuggestions] = useState<
    ServiceAreaSuggestion[]
  >([]);

  // Business search state management
  const [isBusinessRequestInProgress, setIsBusinessRequestInProgress] =
    useState(false);
  const pendingBusinessQueryRef = useRef<string | null>(null);
  const currentBusinessQueryRef = useRef<string>("");

  // Initialize selectedBusiness from configuration
  useEffect(() => {
    if (
      configuration.businessName &&
      configuration.placeId &&
      !selectedBusiness
    ) {
      const businessFromConfig: LocalFalconBusiness = {
        name: configuration.businessName,
        placeId: configuration.placeId,
        address: currentLocation?.address || "",
        lat: configuration.centerLat || 0,
        lng: configuration.centerLng || 0,
        rating: 0,
        totalRatings: 0,
        isServiceAreaBusiness: false,
      };
      setSelectedBusiness(businessFromConfig);
    }
  }, [
    configuration.businessName,
    configuration.placeId,
    currentLocation,
    selectedBusiness,
  ]);

  // Grid size options from 3x3 to 21x21
  const gridSizeOptions = [];
  for (let i = 3; i <= 21; i += 2) {
    gridSizeOptions.push(`${i}x${i}`);
  }

  const unitOptions = [
    { value: "meters", label: "Meters" },
    { value: "kilometers", label: "Kilometers" },
    { value: "miles", label: "Miles" },
  ];

  /**
   * Handles business search with request queuing to prevent overlapping API calls
   *
   * Flow:
   * 1. User types -> handleBusinessSearch called for each character
   * 2. If request in progress -> store query as pending and return
   * 3. If no request in progress -> start new request
   * 4. Since we can't await the parent's API call, we use a timeout to simulate completion
   * 5. After timeout, check if newer query is pending and process it
   * 6. This ensures only the latest user input is processed after current request "completes"
   */
  const handleBusinessSearch = async (query: string) => {
    // If query is too short, clear results and return
    if (query.length < 3) {
      currentBusinessQueryRef.current = "";
      pendingBusinessQueryRef.current = null;
      setIsBusinessRequestInProgress(false);
      setPlaceSuggestions([]);
      return;
    }

    // If a request is already in progress, store this query as pending
    // This prevents multiple simultaneous API calls
    if (isBusinessRequestInProgress) {
      pendingBusinessQueryRef.current = query;
      console.log(
        `Business search: Request in progress, queuing query: "${query}"`
      );
      return;
    }

    // Start the request
    setIsBusinessRequestInProgress(true);
    currentBusinessQueryRef.current = query;
    pendingBusinessQueryRef.current = null;

    console.log(
      `Business search: Starting Google Places request for query: "${query}"`
    );

    try {
      setBusinessLoading(true);

      // Use Google Places API for fast business suggestions
      const response = await googlePlacesService.getServiceAreaSuggestions(
        query
      );

      if (response.success && response.data) {
        console.log(
          `Business search: Found ${response.data.length} suggestions`
        );
        setPlaceSuggestions(response.data);
      } else {
        console.log("Business search: No suggestions found");
        setPlaceSuggestions([]);
      }

      setBusinessLoading(false);

      // After completion, check if there's a pending query
      const pendingQuery = pendingBusinessQueryRef.current;
      if (pendingQuery && pendingQuery !== query) {
        // There's a newer query waiting, process it
        console.log(
          `Business search: Processing pending query: "${pendingQuery}"`
        );
        setIsBusinessRequestInProgress(false);
        handleBusinessSearch(pendingQuery);
      } else {
        // No pending query, we're done
        console.log(`Business search: Request completed for query: "${query}"`);
        setIsBusinessRequestInProgress(false);
      }
    } catch (error) {
      console.error("Error in Google Places business search:", error);
      setPlaceSuggestions([]);
      setIsBusinessRequestInProgress(false);
      setBusinessLoading(false);
    }
  };

  // Convert Google Places suggestion to LocalFalconBusiness format
  const convertPlaceToLocalFalconBusiness = async (
    place: ServiceAreaSuggestion
  ): Promise<LocalFalconBusiness | null> => {
    try {
      // Get place details to get lat/lng coordinates
      const placeDetailsResponse = await googlePlacesService.getPlaceDetails(
        place.placeId
      );

      if (placeDetailsResponse.success && placeDetailsResponse.data) {
        const placeDetails = placeDetailsResponse.data;
        return {
          name: place.placeName,
          address: placeDetails.formattedAddress,
          placeId: place.placeId,
          lat: placeDetails.geometry.location.lat,
          lng: placeDetails.geometry.location.lng,
          rating: 0, // Default values since Google Places API doesn't provide these
          totalRatings: 0,
          isServiceAreaBusiness: false,
        };
      }

      // Fallback if place details fail
      return {
        name: place.placeName,
        address: place.secondaryText || place.placeName,
        placeId: place.placeId,
        lat: 0,
        lng: 0,
        rating: 0,
        totalRatings: 0,
        isServiceAreaBusiness: false,
      };
    } catch (error) {
      console.error("Error converting place to LocalFalconBusiness:", error);
      return null;
    }
  };

  const handleRunScan = () => {
    if (
      !configuration.keyword ||
      !configuration.businessName ||
      !configuration.centerLat ||
      !configuration.centerLng
    ) {
      return;
    }

    const scanRequest: LocalFalconScanRequest = {
      keyword: configuration.keyword,
      businessName: configuration.businessName,
      lat: configuration.centerLat,
      lng: configuration.centerLng,
      gridSize: configuration.gridSize,
      radius: configuration.radius,
      unit: configuration.unit,
      placeId: configuration.placeId,
    };

    onScan(scanRequest);
  };

  const handleGenerateKeywordSuggestions = async (request: {
    businessName: string;
    businessCategory?: string;
    location?: string;
    businessType?: string;
  }) => {
    try {
      const response = await localFalconService.generateKeywordSuggestions(
        request
      );
      return response;
    } catch (error: any) {
      console.error("Error generating keyword suggestions:", error);
      return {
        success: false,
        error: error.message || "Failed to generate keyword suggestions",
      };
    }
  };

  const handleDeleteClick = (configId: number) => {
    setConfigToDelete(configId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (configToDelete) {
      onDeleteConfiguration(configToDelete);
    }
    setDeleteDialogOpen(false);
    setConfigToDelete(null);
  };

  const isConfigurationValid = () => {
    return (
      configuration.keyword &&
      configuration.businessName &&
      configuration.centerLat &&
      configuration.centerLng &&
      configuration.gridSize &&
      configuration.radius > 0
    );
  };

  return (
    <Box>
      {/* Search Configuration */}
      <Card sx={{ mb: 2 }}>
        <CardContent sx={{ p: 2, "&:last-child": { pb: 0 } }}>
          {!isConfigurationValid() && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Please fill in all required fields: keyword, business name,
              location, grid size, and radius.
            </Alert>
          )}

          <Autocomplete
            fullWidth
            options={placeSuggestions}
            getOptionLabel={(option) => option.placeName}
            value={
              selectedBusiness
                ? {
                    placeName: selectedBusiness.name,
                    placeId: selectedBusiness.placeId,
                    mainText: selectedBusiness.name,
                    secondaryText: selectedBusiness.address,
                    types: [],
                  }
                : null
            }
            onChange={async (_, newValue) => {
              if (newValue) {
                // Convert Google Places suggestion to LocalFalconBusiness
                const business = await convertPlaceToLocalFalconBusiness(
                  newValue
                );
                if (business) {
                  setSelectedBusiness(business);
                  onConfigurationChange({
                    businessName: business.name,
                    placeId: business.placeId,
                    centerLat: business.lat,
                    centerLng: business.lng,
                  });

                  // Keywords will be auto-generated by KeywordSuggestions component when business name changes
                }
              } else {
                setSelectedBusiness(null);
              }
            }}
            onInputChange={(_, newInputValue) => {
              handleBusinessSearch(newInputValue);
            }}
            loading={businessLoading}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Business Name"
                placeholder="Search for your business"
                helperText={
                  isBusinessRequestInProgress && pendingBusinessQueryRef.current
                    ? "Request in progress, newer query queued..."
                    : businessLoading
                    ? "Searching for businesses..."
                    : "Type to search for your business"
                }
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {businessLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Box>
                  <Typography variant="body2">{option.mainText}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.secondaryText}
                  </Typography>
                </Box>
              </Box>
            )}
            sx={{ mb: 2 }}
          />

          <KeywordSuggestions
            value={configuration.keyword}
            onChange={(keyword) => onConfigurationChange({ keyword })}
            businessName={configuration.businessName}
            location={
              currentLocation?.address ||
              currentLocation?.name ||
              "Selected Location"
            }
            onGenerateSuggestions={handleGenerateKeywordSuggestions}
            disabled={loading}
          />

          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Grid Size</InputLabel>
              <Select
                value={configuration.gridSize}
                label="Grid Size"
                onChange={(e) =>
                  onConfigurationChange({ gridSize: e.target.value })
                }
              >
                {gridSizeOptions.map((size) => (
                  <MenuItem key={size} value={size}>
                    {size} ({parseInt(size) * parseInt(size)} points)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Radius"
              type="number"
              value={configuration.radius}
              onChange={(e) =>
                onConfigurationChange({ radius: Number(e.target.value) })
              }
              inputProps={{ min: 0.1, step: 0.1 }}
            />

            <FormControl fullWidth>
              <InputLabel>Unit</InputLabel>
              <Select
                value={configuration.unit}
                label="Unit"
                onChange={(e) =>
                  onConfigurationChange({
                    unit: e.target.value as "miles" | "kilometers" | "meters",
                  })
                }
              >
                {unitOptions.map((unit) => (
                  <MenuItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>

          <LoadingButton
            variant="contained"
            onClick={handleRunScan}
            loading={loading}
            disabled={!isConfigurationValid()}
            startIcon={<PlayArrowIcon />}
            sx={{ mb: 2 }}
            className="updatesShapeBtn"
          >
            Run Scan
          </LoadingButton>
        </CardContent>
      </Card>

      {/* Configuration Management */}
      <Card>
        <CardContent sx={{ p: 2, "&:last-child": { pb: 0 } }}>
          <Typography
            variant="subtitle1"
            gutterBottom
            sx={{ display: "flex", alignItems: "center", gap: 1 }}
          >
            Save Configuration
          </Typography>

          <TextField
            fullWidth
            label="Configuration Name"
            value={configuration.name}
            onChange={(e) => onConfigurationChange({ name: e.target.value })}
            sx={{ mb: 2 }}
            placeholder="e.g., Downtown Restaurant Scan"
          />

          <LoadingButton
            variant="contained"
            onClick={onSaveConfiguration}
            loading={loading}
            disabled={!configuration.name.trim() || !isConfigurationValid()}
            startIcon={<SaveIcon />}
            sx={{ mb: 2 }}
            className="updatesShapeBtn"
          >
            Save Configuration
          </LoadingButton>

          {savedConfigurations.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Saved Configurations
              </Typography>
              <List dense>
                {savedConfigurations.map((config) => (
                  <ListItem key={config.id} divider>
                    <ListItemText
                      primary={config.name}
                      secondary={`${config.keyword} - ${config.businessName} (${config.gridSize})`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => onLoadConfiguration(config)}
                        disabled={loading}
                        size="small"
                      >
                        <RefreshIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteClick(config.id!)}
                        disabled={loading}
                        size="small"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Configuration</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this configuration? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            variant="contained"
            onClick={() => setDeleteDialogOpen(false)}
            className="closeFillBtn"
            startIcon={<CancelOutlinedIcon />}
          >
            Cancel
          </Button>
          <Button
            variant="outlined"
            onClick={handleDeleteConfirm}
            color="error"
            autoFocus
            className="updatesShapeBtn"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LocalFalconControls;
